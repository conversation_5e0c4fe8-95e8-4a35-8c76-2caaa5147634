#!/usr/bin/env python3
"""
囲み文字（quote_char）の動作確認テスト
"""

import csv
import io
import tempfile
import os

def test_quote_behavior():
    """囲み文字の動作を確認"""
    
    # テストデータ（囲み文字が必要なデータ）
    test_data = [
        ['ID', '商品名', '価格'],
        ['1', 'test1,test2,test3', '100'],  # カンマを含むデータ
        ['2', 'test"quote"test', '200'],    # ダブルクォーテーションを含むデータ
        ['3', 'normal_data', '300']         # 通常のデータ
    ]
    
    print("=== 元のテストデータ ===")
    for row in test_data:
        print(row)
    
    # CSV形式で出力（quote_char='"', QUOTE_MINIMAL）
    print("\n=== CSV出力（quote_char='\"', QUOTE_MINIMAL）===")
    csv_output = io.StringIO()
    csv_writer = csv.writer(csv_output, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)
    for row in test_data:
        csv_writer.writerow(row)
    csv_content = csv_output.getvalue()
    print(csv_content)
    
    # TSV形式で出力（quote_char='', QUOTE_NONE）
    print("=== TSV出力（quote_char='', QUOTE_NONE）===")
    tsv_output = io.StringIO()
    try:
        tsv_writer = csv.writer(tsv_output, delimiter='\t', quotechar='', quoting=csv.QUOTE_NONE)
        for row in test_data:
            tsv_writer.writerow(row)
        tsv_content = tsv_output.getvalue()
        print(tsv_content)
    except Exception as e:
        print(f"エラー: {e}")
    
    # TSV形式で出力（quote_char='"', QUOTE_MINIMAL）
    print("=== TSV出力（quote_char='\"', QUOTE_MINIMAL）===")
    tsv_output2 = io.StringIO()
    tsv_writer2 = csv.writer(tsv_output2, delimiter='\t', quotechar='"', quoting=csv.QUOTE_MINIMAL)
    for row in test_data:
        tsv_writer2.writerow(row)
    tsv_content2 = tsv_output2.getvalue()
    print(tsv_content2)
    
    # CSV読み込みテスト
    print("=== CSV読み込みテスト ===")
    csv_input = io.StringIO(csv_content)
    csv_reader = csv.reader(csv_input, delimiter=',', quotechar='"')
    for i, row in enumerate(csv_reader):
        print(f"Row {i}: {row}")

if __name__ == "__main__":
    test_quote_behavior()
