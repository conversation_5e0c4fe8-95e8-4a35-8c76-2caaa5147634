#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
from unittest.mock import MagicMock
import boto3
import botocore
import pytest
import tempfile

import yaml
from source.glue_job_convert_format import main
from test.resources import get_test_resource_path

# テスト共通の設定
ETL_CONFIG_BASE = {
    "common": {"format": "csv2tsv", "encoding": "utf-8"},
    "input": {"csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}},
    "output": {
        "tsv_options": {"delimiter": "\t", "quote_char": "", "has_header": True}
    },
}


def cleanup_test_files(file_paths):
    """テスト用ファイルの削除"""
    # for file_path in file_paths:
    #     if file_path and os.path.exists(file_path):
    #         os.unlink(file_path)


def _debug_s3_objects(s3_client: boto3.client, bucket: str, prefix: str = None) -> None:
    """
    S3バケット内のオブジェクトをデバッグ出力する

    Args:
        s3_client: S3クライアント
        bucket: バケット名
        prefix: オブジェクト検索の接頭辞（オプション）
    """
    try:
        # 指定された接頭辞のオブジェクトを検索
        list_kwargs = {"Bucket": bucket}
        if prefix:
            list_kwargs["Prefix"] = prefix

        response = s3_client.list_objects_v2(**list_kwargs)

        # 指定のプレフィックスでオブジェクトが見つかった場合
        if "Contents" in response:
            print(
                f"S3 Debug - Objects in {'prefix: ' + prefix if prefix else 'bucket'}:"
            )
            for obj in response["Contents"]:
                print(f"  - {obj['Key']}")
        else:
            print(
                f"S3 Debug - No objects found {f'with prefix: {prefix}' if prefix else 'in the bucket'}"
            )

            # バケット内の全オブジェクトを確認
            all_objects_response = s3_client.list_objects_v2(Bucket=bucket)
            if "Contents" in all_objects_response:
                print("S3 Debug - All Objects in Bucket:")
                for obj in all_objects_response["Contents"]:
                    print(f"  - {obj['Key']}")
            else:
                print("S3 Debug - No objects found in the entire bucket")

    except Exception as e:
        print(f"S3 Debug - Error listing objects: {e}")


def test_main_csv_to_tsv_normal(monkeypatch, mock_aws, capsys):
    """
    TC-012: CSV(UTF8)→TSV(UTF8) 標準オプション
    main関数の正常系テスト（ETL設定ファイル使用）

    テスト内容:
    - CSVからTSVへの変換
    - ETL設定ファイルを使用した変換
    - 入出力ファイルの分離
    - S3操作の検証
    - ログ出力の検証
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(ETL_CONFIG_BASE, f)

        # テストデータの作成
        test_data = 'id,name,value\n1,"test1",100\n2,"test2",200'
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv"
        ) as temp_csv:
            temp_csv.write(test_data)
            temp_csv_path = temp_csv.name

        # S3への入力ファイルアップロード
        input_file_name = "source_data.csv"  # 入力ファイル名
        output_file_name = "converted_data.tsv"  # 出力ファイル名

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_csv_path, "rb").read(),
        )

        # S3オブジェクトの確認（デバッグ用）
        _debug_s3_objects(s3_client, "test-bucket", "test/input/")

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",  # 入力ファイル名を指定
            input_file_name,
            "--output_file_name",  # 出力ファイル名を指定
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()
        sys.stdout.write(captured.out)  # デバッグ用に出力を表示

        # S3出力の確認（デバッグ用）
        _debug_s3_objects(s3_client, "test-bucket", "test/output/")

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read().decode("utf-8")
        expected_content = "id\tname\tvalue\n1\ttest1\t100\n2\ttest2\t200"

        assert content.strip() == expected_content.strip()

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

        # 入力ファイルの存在確認
        with pytest.raises(Exception):
            s3_client.get_object(
                Bucket="test-bucket", Key=f"test/input/{input_file_name}"
            )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_main_tsv_to_csv_normal(monkeypatch, mock_aws, capsys):
    """
    TC-016: TSV(UTF8)→CSV(UTF8) 標準オプション
    main関数の正常系テスト（TSVからCSV変換）

    目的:
    - TSVからCSVへの変換処理の検証
    - ETL設定ファイルを使用した変換
    - 入出力ファイルの分離
    - S3操作の検証
    - ログ出力の検証
    """
    temp_tsv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        tsv_to_csv_config = {
            "common": {"format": "tsv2csv", "encoding": "utf-8"},
            "input": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",  # クォート無し
                    "has_header": True,
                }
            },
            "output": {
                "csv_options": {
                    "delimiter": ",",
                    "quote_char": '"',  # 必要な場合のみクォート
                    "has_header": True,
                    "line_ending": "\r\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/tsv2csv", "test_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(tsv_to_csv_config, f)

        # テストデータの作成（改行コードを\nで統一）
        test_data = "id\tname\tvalue\n1\ttest1\t100\n2\ttest2\t200"
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".tsv", newline="\n"
        ) as temp_tsv:
            temp_tsv.write(test_data)
            temp_tsv_path = temp_tsv.name

        # S3への入力ファイルアップロード
        input_file_name = "source_data.tsv"  # 入力ファイル名
        output_file_name = "converted_data.csv"  # 出力ファイル名

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_tsv_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "tsv2csv/test_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()
        sys.stdout.write(captured.out)  # デバッグ用に出力を表示

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read().decode("utf-8")

        # 期待値の改行コードを設定ファイルと合わせる
        expected_content = "id,name,value\r\n1,test1,100\r\n2,test2,200"

        # 内容の比較（改行コードも含めて比較）
        assert content.strip() == expected_content.strip()

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_tsv_path, config_path])


def test_main_backup_file(monkeypatch, mock_aws, capsys):
    """
    バックアップ機能の正常系テスト
    TC-024: バックアップ実施（フラグ: True）
    TC-028: 削除成功（バックアップあり）

    テスト内容:
    - バックアップフラグTrueでの正常系処理
    - バックアップファイルの作成確認
    - 元ファイルの正常削除確認
    - ログ出力の検証
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        csv_to_tsv_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True,
                    "line_ending": "\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_backup_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(csv_to_tsv_config, f)

        # テスト用の一時CSVファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            temp_csv.write('id,name,value\n1,"test1",100\n2,"test2",200')
            temp_csv_path = temp_csv.name

        # S3へファイルをアップロード
        input_file_name = "test_backup_source.csv"
        output_file_name = "test_backup_output.tsv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_csv_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_backup_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--backup_file_dir",
            "test/backup",
            "--backup_flag",
            "True",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # バックアップファイルの存在確認
        try:
            backup_response = s3_client.get_object(
                Bucket="test-bucket", Key=f"test/backup/{input_file_name}"
            )
            backup_content = backup_response["Body"].read().decode("utf-8")
            assert backup_content == 'id,name,value\n1,"test1",100\n2,"test2",200'
        except Exception as e:
            pytest.fail(f"バックアップファイルの確認に失敗: {str(e)}")

        # 変換後のファイルの存在確認
        try:
            output_response = s3_client.get_object(
                Bucket="test-bucket", Key=f"test/output/{output_file_name}"
            )
            output_content = output_response["Body"].read().decode("utf-8")
            assert (
                output_content.strip()
                == "id\tname\tvalue\n1\ttest1\t100\n2\ttest2\t200"
            )
        except Exception as e:
            pytest.fail(f"出力ファイルの確認に失敗: {str(e)}")

        # 元ファイルが削除されていることを確認
        with pytest.raises(s3_client.exceptions.NoSuchKey):
            s3_client.get_object(
                Bucket="test-bucket", Key=f"test/input/{input_file_name}"
            )

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_convert_format_job_with_missing_required_parameters(
    monkeypatch, mock_aws, capsys
):
    """
    TC-001: 必須パラメータ未設定時のエラー検証

    Args:
        monkeypatch: システム引数の置き換え用
        mock_aws: AWSモック
        capsys: 標準出力キャプチャ
    """
    # sys.argvのモック（必須パラメータ不足）
    test_args = ["glue_job_convert_format.py"]
    monkeypatch.setattr(sys, "argv", test_args)

    # テスト実行：ValueError例外を期待
    with pytest.raises(ValueError) as excinfo:
        main()

    # エラーメッセージの確認
    assert "Required parameters are missing" in str(excinfo.value)

    # 出力をキャプチャ
    captured_out, _ = capsys.readouterr()

    # キャプチャした出力を再度標準出力に書き戻す
    sys.stdout.write(captured_out)

    # 必須パラメータが見つからないことを確認
    assert (
        "[E_job_convert_format_003]例外発生しました。Required parameters are missing: "
        in str(captured_out)
    )

    # ログ検証
    assert "処理で異常が発生しました" in str(captured_out)
    assert "[E_job_convert_format_002]処理で異常が発生しました。(処理名=" in str(
        captured_out
    )
    assert (
        "[E_job_convert_format_001]ジョブが異常終了しました。(ファイル名=Unknown)"
        in str(captured_out)
    )

    # 「ジョブを開始しました」のログが出力されることを確認
    assert "[I_job_convert_format_001]ジョブを開始しました" in captured_out

    # 「ジョブが正常終了しました」のログが出力されていないことを確認
    assert "ジョブが正常終了しました" not in captured_out


def test_s3_file_retrieval_normal(monkeypatch, mock_aws, capsys):
    """
    TC-002: S3ファイル取得の正常系テスト

    目的:
    - S3からのファイル取得が正常に行われることを検証する
    - 必要なパラメータが設定されている状態での動作を確認する

    検証項目:
    - 開始ログの出力
    - 異常ログの未出力
    - 正常終了ログの出力
    - インプットファイルの正常読込
    """
    try:
        # テスト用の一時CSVファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            test_data = 'id,name,value\n1,"test1",100\n2,"test2",200'
            temp_csv.write(test_data)
            temp_csv_path = temp_csv.name

        # S3クライアントのモック
        s3_client = mock_aws["s3"]

        # S3へファイルをアップロード
        s3_client.put_object(
            Bucket="test-bucket",
            Key="test/input/convert_test.csv",
            Body=open(temp_csv_path, "rb").read(),
        )

        # 環境変数のモック
        monkeypatch.setattr(
            os,
            "environ",
            {
                "S3_BUCKET_NAME": "test-bucket",
                "S3_RETRY_LIMIT": "3",
                "S3_RETRY_INTERVAL": "1.0",
            },
        )

        # sys.argvのモック
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",  # 追加
            "csv2tsv/test_config",  # 適切なETL設定ファイルID
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",  # 追加
            "convert_test.csv",
            "--output_file_name",  # 追加
            "converted_test.tsv",
            "--input_format_options",
            "csv",
            "--output_format_options",
            "tsv",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured.out)

        # 開始ログ出力の検証
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名=convert_test.csv)"
            in captured.out
        )

        # 正常終了ログ出力の検証
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名=convert_test.csv)"
            in captured.out
        )

        # S3出力ファイルの検証
        s3_output = s3_client.get_object(
            Bucket="test-bucket", Key="test/output/converted_test.tsv"
        )
        output_content = s3_output["Body"].read().decode("utf-8")

    finally:
        # 一時ファイルの削除
        if "temp_csv_path" in locals() and os.path.exists(temp_csv_path):
            os.unlink(temp_csv_path)


def test_main_csv_to_tsv_sjis_normal(monkeypatch, mock_aws, capsys):
    """
    TC-010: CSV(SJIS)→TSV(SJIS) 標準オプションのテスト
    TC-002: S3からのファイル正常取得

    前提条件:
    Input:
    - 文字コード:SJIS
    - 区切り文字:カンマ
    - 囲み文字:あり
    - ヘッダ:あり
    - 改行:CRLF
    Output:
    - 区切り文字:タブ
    - 囲み文字:なし（TSV標準仕様）
    - ヘッダ:あり
    - 改行:CRLF

    目的:
    - SJISエンコードのCSVからTSVへの変換検証
    - ETL設定ファイルを使用した変換
    - 文字化けが発生しないことの確認
    - S3操作の検証
    - ログ出力の検証
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        sjis_config = {
            "common": {
                "format": "csv2tsv",
                "encoding": "shift_jis",  # SJISエンコーディング指定
            },
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",  # TSV標準仕様：囲み文字なし
                    "has_header": True,
                    "line_ending": "\r\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_sjis_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(sjis_config, f)

        # SJISのテストデータ作成（RFC4180準拠の正しいCSVデータ）
        test_data = '"ID","商品名","価格","備考"\r\n"1","商品A,特別版","100","通常商品"\r\n"2","商品B""限定""","200","限定商品"'.encode("shift_jis")

        print("1. テストデータ作成直後:", test_data)

        with tempfile.NamedTemporaryFile(
            mode="wb", delete=False, suffix=".csv"
        ) as temp_csv:
            temp_csv.write(test_data)
            temp_csv_path = temp_csv.name

        with open(temp_csv_path, "rb") as f:
            temp_file_content = f.read()
            print("2. tempfile書き込み後:", temp_file_content)

        # S3への入力ファイルアップロード
        input_file_name = "sjis_source.csv"
        output_file_name = "sjis_converted.tsv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=test_data,  # エンコード済みのバイトデータを直接使用
        )

        # バイト列として読み込み
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/input/{input_file_name}"
        )
        s3_content = response["Body"].read()  # バイト列として
        print("2. S3から読み込んだバイト列:", s3_content)

        # バイト列の一致を確認
        if test_data == s3_content:
            print("3. バイト列は完全に一致")
        else:
            print("3. バイト列が異なります")
            print("   - 元のデータ長:", len(test_data))
            print("   - S3データ長:", len(s3_content))

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_sjis_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured.out)

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read()
        expected_content = 'ID\t商品名\t価格\t備考\r\n1\t商品A,特別版\t100\t通常商品\r\n2\t商品B"限定"\t200\t限定商品'.encode(
            "shift_jis"
        )

        # バイトレベルでの比較
        assert content.strip() == expected_content.strip()

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

    finally:
        cleanup_test_files([temp_csv_path, config_path])


# TC-014: TSV(SJIS)→CSV(SJIS)の実装
def test_main_tsv_to_csv_sjis_normal(monkeypatch, mock_aws, capsys):
    """
    TC-014: TSV(SJIS)→CSV(SJIS) 標準オプションのテスト

    目的:
    - SJISエンコードのTSVからCSVへの変換検証
    - ETL設定ファイルを使用した変換
    - 文字化けが発生しないことの確認
    - S3操作の検証
    - ログ出力の検証
    """
    temp_tsv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        sjis_config = {
            "common": {"format": "tsv2csv", "encoding": "shift_jis"},  # SJISを指定
            "input": {
                "tsv_options": {"delimiter": "\t", "quote_char": "", "has_header": True}
            },
            "output": {
                "csv_options": {
                    "delimiter": ",",
                    "quote_char": '"',
                    "has_header": True,
                    "line_ending": "\r\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/tsv2csv", "test_sjis_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(sjis_config, f)

        # SJISのテストデータ作成（日本語を含む）
        test_data = "ID\t名前\t値\n1\tテスト1\t100\n2\tテスト2\t200"
        with tempfile.NamedTemporaryFile(
            mode="w", encoding="shift-jis", delete=False, suffix=".tsv"
        ) as temp_tsv:
            temp_tsv.write(test_data)
            temp_tsv_path = temp_tsv.name

        # S3への入力ファイルアップロード
        input_file_name = "sjis_source.tsv"
        output_file_name = "sjis_converted.csv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_tsv_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "tsv2csv/test_sjis_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read().decode("shift-jis")
        expected_content = "ID,名前,値\r\n1,テスト1,100\r\n2,テスト2,200"

        assert content.strip() == expected_content.strip()

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

    finally:
        cleanup_test_files([temp_tsv_path, config_path])


def test_main_csv_to_tsv_sjis_minimal(monkeypatch, mock_aws, capsys):
    """
    TC-011: CSV(SJIS)→TSV(SJIS) 最小オプション

    テスト内容:
    - 最小設定でのCSV→TSV変換
    - ヘッダーなし
    - 囲み文字なし
    - 改行コードLF
    - 文字コードSJIS
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成（最小設定）
        sjis_config = {
            "common": {
                "format": "csv2tsv",
                "encoding": "shift-jis",  # 内部的にはms932に変換される
            },
            "input": {
                "csv_options": {
                    "delimiter": ",",
                    "quote_char": "",  # 囲み文字なし
                    "has_header": False,  # ヘッダーなし
                }
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",  # 囲み文字なし
                    "has_header": False,  # ヘッダーなし
                    "line_ending": "\n",  # Unix改行
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_sjis_minimal_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(sjis_config, f)

        # SJISのテストデータ作成（ヘッダーなし、最小構成）
        test_data = "商品A,100\n商品B,200\n商品C,300"
        with tempfile.NamedTemporaryFile(
            mode="w", encoding="shift_jis", delete=False, suffix=".csv", newline="\n"
        ) as temp_csv:
            temp_csv.write(test_data)
            temp_csv_path = temp_csv.name

        # S3への入力ファイルアップロード
        input_file_name = "sjis_source_minimal.csv"
        output_file_name = "sjis_converted_minimal.tsv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_csv_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_sjis_minimal_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read().decode("shift_jis")
        expected_content = "商品A\t100\n商品B\t200\n商品C\t300"

        # 内容の比較（改行コードも含めて検証）
        assert content.strip() == expected_content.strip()

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

        # 入力ファイルが削除されていることを確認
        with pytest.raises(Exception):
            s3_client.get_object(
                Bucket="test-bucket", Key=f"test/input/{input_file_name}"
            )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])



def test_main_csv_to_tsv_with_header_definition(monkeypatch, mock_aws, capsys):
    """
    カスタムヘッダ定義 (header_definition.columns) を使用したテスト

    テスト内容:
    - YAMLで定義された日本語ヘッダが出力されること
    - 入力ファイルにヘッダがない場合
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成 (header_definition付き)
        header_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {"csv_options": {"delimiter": ",", "quote_char": '"', "has_header": False}}, # 入力ヘッダなし
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True, # 出力ヘッダあり
                    "line_ending": "\n",
                }
            },
            "header_definition": { # カスタムヘッダ定義 (辞書のリスト形式に変更)
                "columns": [
                    {"name": "受付番号"},
                    {"name": "顧客名"},
                    {"name": "合計金額"}
                ]
            }
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_header_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w", encoding="utf-8") as f:
            yaml.dump(header_config, f, allow_unicode=True)

        # テストデータの作成 (ヘッダなし)
        test_data = '1,"テスト顧客",1000\n2,"サンプル顧客",2500'
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", encoding="utf-8"
        ) as temp_csv:
            temp_csv.write(test_data)
            temp_csv_path = temp_csv.name

        # S3への入力ファイルアップロード
        input_file_name = "header_test_input.csv"
        output_file_name = "header_test_output.tsv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_csv_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_header_config", # 作成した設定ファイルを指定
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_HEADER_TEST",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()
        sys.stdout.write(captured.out)

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read().decode("utf-8")
        # 期待されるヘッダとデータ
        expected_content = "受付番号\t顧客名\t合計金額\n1\tテスト顧客\t1000\n2\tサンプル顧客\t2500"

        assert content.strip() == expected_content.strip()

        # ログ出力の検証 (任意)
        assert (
            f"[INFO][JN_HEADER_TEST][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_HEADER_TEST][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_main_tsv_to_csv_sjis_minimal(monkeypatch, mock_aws, capsys):
    """
    TC-015: TSV(SJIS)→CSV(SJIS) 最小オプション

    テスト内容:
    - 最小設定でのTSV→CSV変換
    - ヘッダーなし
    - 囲み文字なし
    - 改行コードLF
    - 文字コードSJIS
    """
    temp_tsv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成（最小設定）
        minimal_config = {
            "common": {
                "format": "tsv2csv",
                "encoding": "shift-jis",  # 内部的にはms932に変換される
            },
            "input": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",  # 囲み文字なし
                    "has_header": False,  # ヘッダーなし
                }
            },
            "output": {
                "csv_options": {
                    "delimiter": ",",
                    "quote_char": "",  # 囲み文字なし
                    "has_header": False,  # ヘッダーなし
                    "line_ending": "\n",  # Unix改行
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/tsv2csv", "test_sjis_minimal_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(minimal_config, f)

        # SJISのテストデータ作成（ヘッダーなし、タブ区切り）
        test_data = "商品A\t100\n商品B\t200\n商品C\t300"
        with tempfile.NamedTemporaryFile(
            mode="w", encoding="shift_jis", delete=False, suffix=".tsv", newline="\n"
        ) as temp_tsv:
            temp_tsv.write(test_data)
            temp_tsv_path = temp_tsv.name

        # S3への入力ファイルアップロード
        input_file_name = "sjis_source_minimal.tsv"
        output_file_name = "sjis_converted_minimal.csv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_tsv_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "tsv2csv/test_sjis_minimal_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read().decode("shift_jis")
        expected_content = "商品A,100\n商品B,200\n商品C,300"

        # 内容の比較（改行コードも含めて検証）
        assert content.strip() == expected_content.strip()

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

        # 入力ファイルが削除されていることを確認
        with pytest.raises(Exception):
            s3_client.get_object(
                Bucket="test-bucket", Key=f"test/input/{input_file_name}"
            )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_tsv_path, config_path])


def test_main_csv_to_tsv_utf8_minimal(monkeypatch, mock_aws, capsys):
    """
    TC-013: CSV(UTF8)→TSV(UTF8) 最小オプション

    テスト内容:
    - 最小設定でのCSV→TSV変換
    - ヘッダーなし
    - 囲み文字なし
    - 改行コードLF
    - 文字コードUTF-8
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成（最小設定）
        etl_config = {
            "common": {
                "format": "csv2tsv",
                "encoding": "utf-8",
            },
            "input": {
                "csv_options": {
                    "delimiter": ",",
                    "quote_char": "",  # 囲み文字なし
                    "has_header": False,  # ヘッダーなし
                }
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",  # 囲み文字なし
                    "has_header": False,  # ヘッダーなし
                    "line_ending": "\n",  # Unix改行
                }
            },
        }

        # 設定ファイルの保存
        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_utf8_minimal_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(etl_config, f)

        # テストデータの作成（UTF-8、最小構成）
        test_data = "製品A,1000\n製品B,2000\n製品C,3000"
        with tempfile.NamedTemporaryFile(
            mode="w", encoding="utf-8", delete=False, suffix=".csv", newline="\n"
        ) as temp_csv:
            temp_csv.write(test_data)
            temp_csv_path = temp_csv.name

        # S3への入力ファイルアップロード
        input_file_name = "utf8_source_minimal.csv"
        output_file_name = "utf8_converted_minimal.tsv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_csv_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_utf8_minimal_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read().decode("utf-8")
        expected_content = "製品A\t1000\n製品B\t2000\n製品C\t3000"

        # 内容の比較（改行コードも含めて検証）
        assert content.strip() == expected_content.strip()

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

        # 入力ファイルが削除されていることを確認
        with pytest.raises(Exception):
            s3_client.get_object(
                Bucket="test-bucket", Key=f"test/input/{input_file_name}"
            )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_main_tsv_to_csv_utf8_minimal(monkeypatch, mock_aws, capsys):
    """
    TC-016-2: TSV(UTF8)→CSV(UTF8) 最小オプション

    テスト内容:
    - 最小設定でのTSV→CSV変換
    - ヘッダーなし
    - 囲み文字なし
    - 改行コードLF
    - 文字コードUTF-8
    """
    temp_tsv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成（最小設定）
        minimal_config = {
            "common": {
                "format": "tsv2csv",
                "encoding": "utf-8",
            },
            "input": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",  # 囲み文字なし
                    "has_header": False,  # ヘッダーなし
                }
            },
            "output": {
                "csv_options": {
                    "delimiter": ",",
                    "quote_char": "",  # 囲み文字なし
                    "has_header": False,  # ヘッダーなし
                    "line_ending": "\n",  # Unix改行
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/tsv2csv", "test_utf8_minimal_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(minimal_config, f)

        # テストデータ作成（UTF-8、タブ区切り）
        test_data = "商品D\t4000\n商品E\t5000\n商品F\t6000"
        with tempfile.NamedTemporaryFile(
            mode="w", encoding="utf-8", delete=False, suffix=".tsv", newline="\n"
        ) as temp_tsv:
            temp_tsv.write(test_data)
            temp_tsv_path = temp_tsv.name

        # S3への入力ファイルアップロード
        input_file_name = "utf8_source_minimal.tsv"
        output_file_name = "utf8_converted_minimal.csv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_tsv_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "tsv2csv/test_utf8_minimal_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read().decode("utf-8")
        expected_content = "商品D,4000\n商品E,5000\n商品F,6000"

        # 内容の比較（改行コードも含めて検証）
        assert content.strip() == expected_content.strip()

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

        # 入力ファイルが削除されていることを確認
        with pytest.raises(Exception):
            s3_client.get_object(
                Bucket="test-bucket", Key=f"test/input/{input_file_name}"
            )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_tsv_path, config_path])


def test_main_fixed_to_tsv_sjis_normal(monkeypatch, mock_aws, capsys):
    """
    TC-017: 固定長(SJIS)→TSV(SJIS) 3項目定義・標準オプション

    テスト内容:
    - 固定長形式（SJIS）からTSVへの変換検証
    - 3項目構成：名前(10byte), 年齢(8byte), 住所(10byte)
    - 改行なしの固定長データ（1レコード28バイト）
    - ETL設定ファイルを使用した変換
    - 標準オプション（ヘッダーあり、囲み文字あり）
    """
    temp_fixed_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        fixed_config = {
            "common": {
                "format": "fixed2tsv",
                "encoding": "shift_jis",  # 内部的にはms932に変換
            },
            "input": {
                "fixed_options": {
                    "total_length": 28,  # 全体のバイト長
                    "fields": [
                        {
                            "name": "名前",
                            "length": 10,
                            "type": "string",
                        },
                        {
                            "name": "年齢",
                            "length": 8,
                            "type": "string",
                        },
                        {
                            "name": "住所",
                            "length": 10,
                            "type": "string",
                        },
                    ],
                }
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": '"',  # ダブルクォートで囲む
                    "has_header": True,  # ヘッダー行あり
                    "line_ending": "\r\n",  # Windows改行
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/fixed2tsv", "test_sjis_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(fixed_config, f)

        # SJISのテストデータ作成（改行なし）
        test_data = (
            "田中太郎  25      東京都    "  # 28バイト
            "山田花子  30      大阪府    "  # 28バイト
            "佐藤次郎  35      福岡県    "  # 28バイト
        )
        # 一時ファイルの作成
        temp_fixed = tempfile.NamedTemporaryFile(delete=False, suffix=".txt")
        temp_fixed_path = temp_fixed.name
        temp_fixed.close()

        # バイナリモードで書き込み
        with open(temp_fixed_path, "wb") as f:
            f.write(test_data.encode("shift-jis"))

        # S3への入力ファイルアップロード
        input_file_name = "fixed_source.txt"
        output_file_name = "fixed_converted.tsv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_fixed_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "fixed2tsv/test_sjis_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured_out, _ = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured_out)

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read().decode("shift-jis")
        expected_content = (
            '"名前"\t"年齢"\t"住所"\r\n'
            '"田中太郎"\t"25"\t"東京都"\r\n'
            '"山田花子"\t"30"\t"大阪府"\r\n'
            '"佐藤次郎"\t"35"\t"福岡県"'
        )

        assert content.strip() == expected_content.strip()

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured_out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured_out
        )

        # 入力ファイルが削除されていることを確認
        with pytest.raises(Exception):
            s3_client.get_object(
                Bucket="test-bucket", Key=f"test/input/{input_file_name}"
            )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_fixed_path, config_path])


def test_main_fixed_to_tsv_sjis_minimal(monkeypatch, mock_aws, capsys):
    """
    TC-018: 固定長(SJIS)→TSV(SJIS) 5項目定義・最小オプション

    テスト内容:
    - 固定長形式（SJIS）からTSVへの変換検証
    - 5項目構成：商品コード(10byte), 商品名(20byte), 価格(10byte), 在庫数(5byte), 更新日(8byte)
    - 改行なしの固定長データ（1レコード53バイト）
    - 最小オプション
      - ヘッダーなし
      - 囲み文字なし
      - 改行コードLF
    """
    temp_fixed_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        fixed_config = {
            "common": {
                "format": "fixed2tsv",
                "encoding": "shift_jis",  # 内部的にはms932に変換
            },
            "input": {
                "fixed_options": {
                    "total_length": 53,  # 全体のバイト長
                    "fields": [
                        {
                            "name": "商品コード",
                            "length": 10,
                            "type": "string",
                        },
                        {
                            "name": "商品名",
                            "length": 20,
                            "type": "string",
                        },
                        {
                            "name": "価格",
                            "length": 10,
                            "type": "string",
                        },
                        {
                            "name": "在庫数",
                            "length": 5,
                            "type": "string",
                        },
                        {
                            "name": "更新日",
                            "length": 8,
                            "type": "string",
                        },
                    ],
                }
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",  # 囲み文字なし
                    "has_header": False,  # ヘッダーなし
                    "line_ending": "\n",  # Unix改行
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/fixed2tsv", "test_sjis_minimal_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(fixed_config, f)

        # SJISのテストデータ作成（改行なし）
        test_data = (
            "ABC00001  オレンジジュース    500       120  20240401"  # 53バイト
            "ABC00002  アップルジュース    550       85   20240402"  # 53バイト
            "ABC00003  グレープジュース    600       95   20240403"  # 53バイト
        )
        # 一時ファイルの作成
        temp_fixed = tempfile.NamedTemporaryFile(delete=False, suffix=".txt")
        temp_fixed_path = temp_fixed.name
        temp_fixed.close()

        # バイナリモードでSJISエンコードして書き込み
        with open(temp_fixed_path, "wb") as f:
            f.write(test_data.encode("shift-jis"))

        # S3への入力ファイルアップロード
        input_file_name = "fixed_source_minimal.txt"
        output_file_name = "fixed_converted_minimal.tsv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_fixed_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "fixed2tsv/test_sjis_minimal_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # 変換結果の検証
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"test/output/{output_file_name}"
        )
        content = response["Body"].read().decode("shift-jis")

        # 期待値（改行コードLF、囲み文字なし）
        expected_content = (
            "ABC00001\tオレンジジュース\t500\t120\t20240401\n"
            "ABC00002\tアップルジュース\t550\t85\t20240402\n"
            "ABC00003\tグレープジュース\t600\t95\t20240403"
        )

        assert content.strip() == expected_content.strip()

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

        # 入力ファイルが削除されていることを確認
        with pytest.raises(Exception):
            s3_client.get_object(
                Bucket="test-bucket", Key=f"test/input/{input_file_name}"
            )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_fixed_path, config_path])


def test_main_no_backup_file(monkeypatch, mock_aws, capsys):
    """
    バックアップなしの場合のテスト
    TC-025: バックアップ未実施（フラグ: False）
    TC-029: 削除成功（バックアップなし）

    テスト内容:
    - バックアップフラグFalseでの正常系処理
    - バックアップファイルが作成されないことの確認
    - 元ファイルの正常削除確認
    - ログ出力の検証
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        csv_to_tsv_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True,
                    "line_ending": "\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_no_backup_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(csv_to_tsv_config, f)

        # テスト用の一時CSVファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            temp_csv.write('id,name,value\n1,"test1",100\n2,"test2",200')
            temp_csv_path = temp_csv.name

        # S3へファイルをアップロード
        input_file_name = "test_no_backup_source.csv"
        output_file_name = "test_no_backup_output.tsv"

        s3_client = mock_aws["s3"]
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"test/input/{input_file_name}",
            Body=open(temp_csv_path, "rb").read(),
        )

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_no_backup_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # バックアップファイルが作成されていないことを確認
        try:
            # キーの存在確認
            response = s3_client.list_objects_v2(
                Bucket="test-bucket", Prefix=f"test/backup/{input_file_name}"
            )
            assert "Contents" not in response, "バックアップファイルが存在しています"
        except s3_client.exceptions.NoSuchKey:
            pass  # これは期待される動作

        # 変換後のファイルの存在確認
        try:
            output_response = s3_client.get_object(
                Bucket="test-bucket", Key=f"test/output/{output_file_name}"
            )
            output_content = output_response["Body"].read().decode("utf-8")
            assert (
                output_content.strip()
                == "id\tname\tvalue\n1\ttest1\t100\n2\ttest2\t200"
            )
        except Exception as e:
            pytest.fail(f"出力ファイルの確認に失敗: {str(e)}")

        # 元ファイルが削除されていることを確認
        with pytest.raises(s3_client.exceptions.NoSuchKey):
            s3_client.get_object(
                Bucket="test-bucket", Key=f"test/input/{input_file_name}"
            )

        # ログ出力の検証
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_001]ジョブを開始しました。(ファイル名={input_file_name})"
            in captured.out
        )
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_s3_upload_retry_success(monkeypatch, mock_aws, capsys):
    """
    TC-022: S3ファイル配置 リトライ後成功

    テスト内容:
    - put_objectを2回失敗させ、3回目に成功させる
    - リトライ処理が正しく動作することを確認
    - リトライログの出力を確認
    - 最終的にファイル配置が成功することを確認
    """
    temp_csv_path = None
    config_path = None
    try:
        # モック環境の設定
        monkeypatch.setenv("AWS_DEFAULT_REGION", "ap-northeast-1")
        monkeypatch.setenv("S3_RETRY_LIMIT", "3")
        monkeypatch.setenv("S3_RETRY_INTERVAL", "1")
        monkeypatch.setenv("S3_BUCKET_NAME", "test-bucket")

        # ETL設定ファイルの作成
        csv_to_tsv_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True,
                    "line_ending": "\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_retry_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(csv_to_tsv_config, f)

        # テスト用の一時CSVファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            temp_csv.write('id,name,value\n1,"test1",100\n2,"test2",200')
            temp_csv_path = temp_csv.name

        # S3クライアントのモック化
        mock_s3_client = MagicMock()

        # get_objectのモック化
        mock_body = MagicMock()
        mock_body.read.return_value = b'id,name,value\n1,"test1",100\n2,"test2",200'
        mock_response = {"Body": mock_body}
        mock_s3_client.get_object.return_value = mock_response

        # put_objectのモック化（2回失敗、3回目成功）
        mock_s3_client.put_object.side_effect = [
            botocore.exceptions.ClientError(
                {"Error": {"Code": "InternalError", "Message": "Internal Error"}},
                "put_object",
            ),  # 1回目失敗
            botocore.exceptions.ClientError(
                {"Error": {"Code": "InternalError", "Message": "Internal Error"}},
                "put_object",
            ),  # 2回目失敗
            None,  # 3回目成功
        ]

        # モックの置き換え
        original_client = boto3.client

        def mock_boto3_client(service, **kwargs):
            if service == "s3":
                return mock_s3_client
            return original_client(service, **kwargs)

        monkeypatch.setattr("boto3.client", mock_boto3_client)

        # find_s3_prefix関数のモック化
        def mock_find_s3_prefix(aws_s3_dir, bucket_name):
            """find_s3_prefix関数のモック - テスト用の固定値を返す"""
            return (aws_s3_dir + "/", "")  # プレフィックスと空の日付文字列を返す

        # glue_job_convert_formatモジュール内でインポートされた関数をモック化
        monkeypatch.setattr("source.glue_job_convert_format.find_s3_prefix", mock_find_s3_prefix)

        # 入力パラメータの設定
        input_file_name = "retry_test_source.csv"
        output_file_name = "retry_test_output.tsv"

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_retry_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # リトライログの検証（2回のリトライログを確認）
        retry_logs = [
            line
            for line in captured.out.split("\n")
            if "[INFO][JN_CP001-DF01_001][I_job_convert_format_003]処理をリトライしました。(処理名=S3ファイル配置)"
            in line
        ]
        assert len(retry_logs) == 2, "リトライログが2回出力されていません"

        # put_objectが3回呼ばれたことを確認
        assert (
            mock_s3_client.put_object.call_count == 3
        ), "put_objectの呼び出し回数が3回ではありません"

        # 正常終了ログの確認
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_s3_upload_retry_exceeded(monkeypatch, mock_aws, capsys):
    """
    TC-023: S3ファイル配置 リトライ回数超過

    テスト内容:
    - put_objectを全回数（3回）失敗させる
    - リトライ上限に達することを確認
    - エラーログの出力を確認
    - 適切な例外が発生することを確認
    """
    temp_csv_path = None
    config_path = None
    try:
        # モック環境の設定
        monkeypatch.setenv("AWS_DEFAULT_REGION", "ap-northeast-1")
        monkeypatch.setenv("S3_RETRY_LIMIT", "3")
        monkeypatch.setenv("S3_RETRY_INTERVAL", "1")
        monkeypatch.setenv("S3_BUCKET_NAME", "test-bucket")

        # ETL設定ファイルの作成
        csv_to_tsv_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True,
                    "line_ending": "\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_retry_exceed_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(csv_to_tsv_config, f)

        # テスト用の一時ファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            temp_csv.write('id,name,value\n1,"test1",100\n2,"test2",200')
            temp_csv_path = temp_csv.name

        # S3クライアントのモック化
        mock_s3_client = MagicMock()

        # get_objectのモック化
        mock_body = MagicMock()
        mock_body.read.return_value = b'id,name,value\n1,"test1",100\n2,"test2",200'
        mock_response = {"Body": mock_body}
        mock_s3_client.get_object.return_value = mock_response

        # put_objectのモック化（全回失敗）
        error = botocore.exceptions.ClientError(
            {"Error": {"Code": "InternalError", "Message": "Internal Error"}},
            "put_object",
        )
        mock_s3_client.put_object.side_effect = error

        # モックの置き換え
        original_client = boto3.client

        def mock_boto3_client(service, **kwargs):
            if service == "s3":
                return mock_s3_client
            return original_client(service, **kwargs)

        monkeypatch.setattr("boto3.client", mock_boto3_client)

        # find_s3_prefix関数のモック化
        def mock_find_s3_prefix(aws_s3_dir, bucket_name):
            """find_s3_prefix関数のモック - テスト用の固定値を返す"""
            return (aws_s3_dir + "/", "")  # プレフィックスと空の日付文字列を返す

        # glue_job_convert_formatモジュール内でインポートされた関数をモック化
        monkeypatch.setattr("source.glue_job_convert_format.find_s3_prefix", mock_find_s3_prefix)

        # 入力パラメータの設定
        input_file_name = "retry_exceed_source.csv"
        output_file_name = "retry_exceed_output.tsv"

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_retry_exceed_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行（例外が発生することを期待）
        with pytest.raises(botocore.exceptions.ClientError) as exc_info:
            main()

        # エラーコードの検証
        assert exc_info.value.response['Error']['Code'] == 'InternalError'

        captured = capsys.readouterr()

        # リトライログの検証（2回のリトライログを確認）
        retry_logs = [
            line
            for line in captured.out.split("\n")
            if "[INFO][JN_CP001-DF01_001][I_job_convert_format_003]処理をリトライしました。(処理名=S3ファイル配置)"
            in line
        ]
        assert len(retry_logs) == 2, "リトライログが2回出力されていません"

        # エラーログの確認
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_convert_format_002]処理で異常が発生しました。(処理名=S3ファイル配置)"
            in captured.out
        )

        # put_objectが3回呼ばれたことを確認
        assert (
            mock_s3_client.put_object.call_count == 3
        ), "put_objectの呼び出し回数が3回ではありません"

        # 正常終了ログが出力されていないことを確認
        assert "ジョブが正常終了しました" not in captured.out

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_backup_file_retry(monkeypatch, mock_aws, capsys):
    """
    TC-026: バックアップ失敗時のリトライ

    テスト内容:
    - バックアップフラグTrueの状態
    - S3のcopy_objectメソッドを2回失敗させ、3回目に成功させる
    - リトライ処理が正しく動作することを確認
    - リトライログの出力を検証
    - 最終的にバックアップが成功することを確認
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        csv_to_tsv_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True,
                    "line_ending": "\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_backup_retry_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(csv_to_tsv_config, f)

        # テスト用の一時CSVファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            temp_csv.write('id,name,value\n1,"test1",100\n2,"test2",200')
            temp_csv_path = temp_csv.name

        # S3クライアントのモック化
        mock_s3_client = MagicMock()

        # クラス変数を追加
        mock_s3_client._backup_retry_count = 0
        mock_s3_client._input_file_state = True  # ファイルが存在する状態から開始

        # モックメソッドの実装
        def mock_get_object(**kwargs):
            if not mock_s3_client._input_file_state:
                # ファイルが削除されている場合はNoSuchKey例外を発生
                error_response = {
                    "Error": {
                        "Code": "NoSuchKey",
                        "Message": "The specified key does not exist",
                    }
                }
                raise botocore.exceptions.ClientError(error_response, "GetObject")

            # データの準備
            mock_body = MagicMock()
            mock_body.read.return_value = b'id,name,value\n1,"test1",100\n2,"test2",200'
            return {"Body": mock_body}

        def mock_copy_object(**kwargs):
            # バックアップリトライカウンターをインクリメント
            mock_s3_client._backup_retry_count += 1

            # 最初の2回は失敗、3回目は成功
            if mock_s3_client._backup_retry_count <= 2:
                error_response = {
                    "Error": {"Code": "InternalError", "Message": "Backup Copy Failed"}
                }
                raise botocore.exceptions.ClientError(error_response, "CopyObject")

            # 3回目は成功
            return {}

        def mock_delete_object(**kwargs):
            # ファイルの状態を削除済みに変更
            mock_s3_client._input_file_state = False
            return {}

        # モックメソッドの設定
        mock_s3_client.get_object.side_effect = mock_get_object
        mock_s3_client.copy_object.side_effect = mock_copy_object
        mock_s3_client.delete_object.side_effect = mock_delete_object

        # 例外クラスの追加
        mock_s3_client.exceptions = type(
            "Exceptions", (), {"NoSuchKey": botocore.exceptions.ClientError}
        )

        # モックの置き換え
        original_client = boto3.client

        def mock_boto3_client(service, **kwargs):
            if service == "s3":
                return mock_s3_client
            return original_client(service, **kwargs)

        monkeypatch.setattr("boto3.client", mock_boto3_client)

# find_s3_prefix関数のモック化
        def mock_find_s3_prefix(aws_s3_dir, bucket_name):
            """find_s3_prefix関数のモック - テスト用の固定値を返す"""
            return (aws_s3_dir + "/", "")  # プレフィックスと空の日付文字列を返す

        # glue_job_convert_formatモジュール内でインポートされた関数をモック化
        monkeypatch.setattr("source.glue_job_convert_format.find_s3_prefix", mock_find_s3_prefix)
        # find_s3_prefix関数のモック化
        def mock_find_s3_prefix(aws_s3_dir, bucket_name):
            """find_s3_prefix関数のモック - テスト用の固定値を返す"""
            return (aws_s3_dir + "/", "")  # プレフィックスと空の日付文字列を返す

        # glue_job_convert_formatモジュール内でインポートされた関数をモック化
        monkeypatch.setattr("source.glue_job_convert_format.find_s3_prefix", mock_find_s3_prefix)

        # 入力パラメータの設定
        input_file_name = "backup_retry_source.csv"
        output_file_name = "backup_retry_output.tsv"

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_backup_retry_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--backup_file_dir",
            "test/backup",
            "--backup_flag",
            "True",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # リトライログの検証（2回のリトライログを確認）
        retry_logs = [
            line
            for line in captured.out.split("\n")
            if "[INFO][JN_CP001-DF01_001][I_job_convert_format_003]処理をリトライしました。(処理名=インプットファイルバックアップ)"
            in line
        ]
        assert len(retry_logs) == 2, "リトライログが2回出力されていません"

        # copy_objectが3回呼ばれたことを確認
        assert (
            mock_s3_client._backup_retry_count == 3
        ), "copy_objectの呼び出し回数が3回ではありません"

        # 正常終了ログの確認
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        )

        # 入力ファイルが削除されていることを確認
        assert not mock_s3_client._input_file_state, "入力ファイルが削除されていません"

        # 削除メソッドが呼び出されたことを確認
        assert (
            mock_s3_client.delete_object.called
        ), "delete_objectが呼び出されていません"
        mock_s3_client.delete_object.assert_called_with(
            Bucket="test-bucket", Key=f"test/input/{input_file_name}"
        )

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_backup_retry_success(monkeypatch, mock_aws, capsys):
    """
    TC-027: インプットファイルバックアップ リトライ後成功

    テスト内容:
    - バックアップフラグTrueの状態
    - S3のcopy_objectメソッドを2回失敗させ、3回目に成功させる
    - 2回のリトライ後にバックアップが成功すること
    - リトライログが出力されること
    - バックアップディレクトリにファイルがコピーされること
    - 正常終了ログが出力されること
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        csv_to_tsv_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True,
                    "line_ending": "\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_backup_retry_success_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(csv_to_tsv_config, f)

        # テスト用の一時CSVファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            temp_csv.write('id,name,value\n1,"test1",100\n2,"test2",200')
            temp_csv_path = temp_csv.name

        # S3クライアントのモック化
        mock_s3_client = MagicMock()

        # テストデータの準備
        sample_data = b'id,name,value\n1,"test1",100\n2,"test2",200'

        # バックアップ処理の状態管理
        backup_state = {
            "retry_count": 0,
            "backup_successful": False,
            "backup_data": None,
        }

        # get_objectのモック化
        def mock_get_object(**kwargs):
            # データの準備
            mock_body = MagicMock()
            mock_body.read.return_value = sample_data
            return {"Body": mock_body}

        # copy_objectのモック化
        def mock_copy_object(**kwargs):
            # リトライ回数を増やす
            backup_state["retry_count"] += 1

            # 最初の2回は失敗、3回目は成功
            if backup_state["retry_count"] <= 2:
                error_response = {
                    "Error": {"Code": "InternalError", "Message": "Backup Copy Failed"}
                }
                raise botocore.exceptions.ClientError(error_response, "CopyObject")

            # 3回目は成功
            backup_state["backup_successful"] = True
            backup_state["backup_data"] = sample_data
            return {}

        # delete_objectのモック化
        def mock_delete_object(**kwargs):
            return {}

        # モックメソッドの設定
        mock_s3_client.get_object.side_effect = mock_get_object
        mock_s3_client.copy_object.side_effect = mock_copy_object
        mock_s3_client.delete_object.side_effect = mock_delete_object

        # 例外クラスの追加
        mock_s3_client.exceptions = type(
            "Exceptions", (), {"NoSuchKey": botocore.exceptions.ClientError}
        )

        # モックの置き換え
        original_client = boto3.client

        def mock_boto3_client(service, **kwargs):
            if service == "s3":
                return mock_s3_client
            return original_client(service, **kwargs)

        monkeypatch.setattr("boto3.client", mock_boto3_client)

# find_s3_prefix関数のモック化
        def mock_find_s3_prefix(aws_s3_dir, bucket_name):
            """find_s3_prefix関数のモック - テスト用の固定値を返す"""
            return (aws_s3_dir + "/", "")  # プレフィックスと空の日付文字列を返す

        # glue_job_convert_formatモジュール内でインポートされた関数をモック化
        monkeypatch.setattr("source.glue_job_convert_format.find_s3_prefix", mock_find_s3_prefix)
        # 入力パラメータの設定
        input_file_name = "backup_retry_success_source.csv"
        output_file_name = "backup_retry_success_output.tsv"

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_backup_retry_success_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--backup_file_dir",
            "test/backup",
            "--backup_flag",
            "True",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # リトライログの検証（2回のリトライログを確認）
        retry_logs = [
            line
            for line in captured.out.split("\n")
            if "[INFO][JN_CP001-DF01_001][I_job_convert_format_003]処理をリトライしました。(処理名=インプットファイルバックアップ)"
            in line
        ]
        assert len(retry_logs) == 2, "リトライログが2回出力されていません"

        # バックアップ処理の検証
        assert (
            backup_state["retry_count"] == 3
        ), "copy_objectの呼び出し回数が3回ではありません"
        assert backup_state[
            "backup_successful"
        ], "バックアップが最終的に成功していません"
        assert (
            backup_state["backup_data"] is not None
        ), "バックアップデータが保存されていません"

        # 正常終了ログの確認
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        ), "正常終了ログが出力されていません"

        # 異常ログが出力されていないことを確認
        error_logs = [line for line in captured.out.split("\n") if "[ERROR]" in line]
        assert len(error_logs) == 0, "異常ログが出力されています"

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_delete_file_retry_failure(monkeypatch, mock_aws, capsys):
    """
    TC-030: インプットファイル削除 削除失敗時のリトライ

    テスト内容:
    - バックアップフラグの状態に関わらず実行
    - S3のdelete_objectメソッドを全リトライ回数分失敗させる
    - リトライ処理が行われること
    - リトライログが出力されること
    - 異常ログが出力されること
    - ファイルが削除されないこと
    - ジョブが異常終了すること
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        csv_to_tsv_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True,
                    "line_ending": "\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_delete_retry_failure_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(csv_to_tsv_config, f)

        # テスト用の一時CSVファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            temp_csv.write('id,name,value\n1,"test1",100\n2,"test2",200')
            temp_csv_path = temp_csv.name

        # S3クライアントのモック化
        mock_s3_client = MagicMock()

        # 削除処理の状態管理
        delete_state = {
            "retry_count": 0,
            "delete_successful": False,
            "input_file_exists": True,
        }

        # get_objectのモック化
        def mock_get_object(**kwargs):
            # データの準備
            mock_body = MagicMock()
            mock_body.read.return_value = b'id,name,value\n1,"test1",100\n2,"test2",200'
            return {"Body": mock_body}

        # delete_objectのモック化
        def mock_delete_object(**kwargs):
            # リトライ回数を増やす
            delete_state["retry_count"] += 1

            # 全リトライ回数分（環境変数のS3_RETRY_LIMITに基づく）失敗させる
            error_response = {
                "Error": {"Code": "InternalError", "Message": "Delete Object Failed"}
            }
            raise botocore.exceptions.ClientError(error_response, "DeleteObject")

        # モックメソッドの設定
        mock_s3_client.get_object.side_effect = mock_get_object
        mock_s3_client.delete_object.side_effect = mock_delete_object

        # 例外クラスの追加
        mock_s3_client.exceptions = type(
            "Exceptions", (), {"NoSuchKey": botocore.exceptions.ClientError}
        )

        # モックの置き換え
        original_client = boto3.client

        def mock_boto3_client(service, **kwargs):
            if service == "s3":
                return mock_s3_client
            return original_client(service, **kwargs)

        monkeypatch.setattr("boto3.client", mock_boto3_client)

# find_s3_prefix関数のモック化
        def mock_find_s3_prefix(aws_s3_dir, bucket_name):
            """find_s3_prefix関数のモック - テスト用の固定値を返す"""
            return (aws_s3_dir + "/", "")  # プレフィックスと空の日付文字列を返す

        # glue_job_convert_formatモジュール内でインポートされた関数をモック化
        monkeypatch.setattr("source.glue_job_convert_format.find_s3_prefix", mock_find_s3_prefix)
        # 環境変数のモック (リトライ回数を設定)
        monkeypatch.setenv("S3_RETRY_LIMIT", "3")
        monkeypatch.setenv("S3_RETRY_INTERVAL", "1")

        # 入力パラメータの設定
        input_file_name = "delete_retry_failure_source.csv"
        output_file_name = "delete_retry_failure_output.tsv"

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_delete_retry_failure_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行（異常終了を期待）
        with pytest.raises(botocore.exceptions.ClientError) as exc_info:
            main()

        # エラーコードの検証
        assert exc_info.value.response['Error']['Code'] == 'InternalError'

        # キャプチャされた出力
        captured = capsys.readouterr()

        # リトライログの検証（指定回数分のリトライログを確認）
        retry_logs = [
            line
            for line in captured.out.split("\n")
            if "[INFO][JN_CP001-DF01_001][I_job_convert_format_003]処理をリトライしました。(処理名=インプットファイル削除)"
            in line
        ]
        assert (
            len(retry_logs) == 2
        ), f"リトライログが2回出力されていません。出力されたログ: {retry_logs}"

        # delete_objectが3回呼ばれたことを確認
        assert (
            mock_s3_client.delete_object.call_count == 3
        ), "delete_objectの呼び出し回数が3回ではありません"

        # 異常ログの確認
        error_logs = [
            line
            for line in captured.out.split("\n")
            if "[ERROR][JN_CP001-DF01_001][E_job_convert_format_002]処理で異常が発生しました。(処理名=インプットファイル削除)"
            in line
        ]
        assert len(error_logs) > 0, "削除処理の異常ログが出力されていません"

        # 正常終了ログが出力されることを確認（削除処理は finally ブロックで実行されるため）
        success_logs = [
            line
            for line in captured.out.split("\n")
            if "ジョブが正常終了しました" in line
        ]
        assert len(success_logs) == 1, "正常終了ログが出力されていません"

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_delete_retry_success(monkeypatch, mock_aws, capsys):
    """
    TC-031: リトライ後削除成功

    テスト内容:
    - モックで2回削除処理を失敗させる
    - 3回目の削除処理で成功させる
    - 2回リトライしたうえで削除を完了すること
    - リトライログを出力すること
    - 正常終了ログを出力すること
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        csv_to_tsv_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True,
                    "line_ending": "\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_delete_retry_success_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(csv_to_tsv_config, f)

        # テスト用の一時CSVファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            temp_csv.write('id,name,value\n1,"test1",100\n2,"test2",200')
            temp_csv_path = temp_csv.name

        # S3クライアントのモック化
        mock_s3_client = MagicMock()

        # 削除処理の状態管理
        delete_state = {
            "retry_count": 0,
            "delete_successful": False,
            "input_file_exists": True,
        }

        # get_objectのモック化
        def mock_get_object(**kwargs):
            # データの準備
            mock_body = MagicMock()
            mock_body.read.return_value = b'id,name,value\n1,"test1",100\n2,"test2",200'
            return {"Body": mock_body}

        # delete_objectのモック化
        def mock_delete_object(**kwargs):
            # リトライ回数を増やす
            delete_state["retry_count"] += 1

            # 最初の2回は失敗、3回目は成功
            if delete_state["retry_count"] <= 2:
                error_response = {
                    "Error": {
                        "Code": "InternalError",
                        "Message": "Delete Object Failed",
                    }
                }
                raise botocore.exceptions.ClientError(error_response, "DeleteObject")

            # 3回目は成功
            delete_state["delete_successful"] = True
            delete_state["input_file_exists"] = False
            return {}

        # モックメソッドの設定
        mock_s3_client.get_object.side_effect = mock_get_object
        mock_s3_client.delete_object.side_effect = mock_delete_object

        # 例外クラスの追加
        mock_s3_client.exceptions = type(
            "Exceptions", (), {"NoSuchKey": botocore.exceptions.ClientError}
        )

        # モックの置き換え
        original_client = boto3.client

        def mock_boto3_client(service, **kwargs):
            if service == "s3":
                return mock_s3_client
            return original_client(service, **kwargs)

        monkeypatch.setattr("boto3.client", mock_boto3_client)

# find_s3_prefix関数のモック化
        def mock_find_s3_prefix(aws_s3_dir, bucket_name):
            """find_s3_prefix関数のモック - テスト用の固定値を返す"""
            return (aws_s3_dir + "/", "")  # プレフィックスと空の日付文字列を返す

        # glue_job_convert_formatモジュール内でインポートされた関数をモック化
        monkeypatch.setattr("source.glue_job_convert_format.find_s3_prefix", mock_find_s3_prefix)
        # 環境変数のモック (リトライ回数を設定)
        monkeypatch.setenv("S3_RETRY_LIMIT", "3")
        monkeypatch.setenv("S3_RETRY_INTERVAL", "1")

        # 入力パラメータの設定
        input_file_name = "delete_retry_success_source.csv"
        output_file_name = "delete_retry_success_output.tsv"

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_delete_retry_success_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # リトライログの検証（2回のリトライログを確認）
        retry_logs = [
            line
            for line in captured.out.split("\n")
            if "[INFO][JN_CP001-DF01_001][I_job_convert_format_003]処理をリトライしました。(処理名=インプットファイル削除)"
            in line
        ]
        assert (
            len(retry_logs) == 2
        ), f"リトライログが2回出力されていません。出力されたログ: {retry_logs}"

        # delete_objectが3回呼ばれたことを確認
        assert (
            mock_s3_client.delete_object.call_count == 3
        ), "delete_objectの呼び出し回数が3回ではありません"

        # 削除が最終的に成功したことを確認
        assert delete_state["delete_successful"], "削除が最終的に成功していません"
        assert not delete_state["input_file_exists"], "入力ファイルが削除されていません"

        # 正常終了ログの確認
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        ), "正常終了ログが出力されていません"

        # 異常ログが出力されていないことを確認
        error_logs = [line for line in captured.out.split("\n") if "[ERROR]" in line]
        assert (
            len(error_logs) == 0
        ), f"異常ログが出力されています。出力されたエラーログ: {error_logs}"

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_s3_file_retrieval_retry_exceeded(monkeypatch, mock_aws, capsys):
    """
    TC-003: S3ファイル取得のリトライ回数超過

    テスト内容:
    - S3のget_objectメソッドを全リトライ回数分失敗させる
    - リトライ処理が行われること
    - リトライログが出力されること
    - 異常ログが出力されること
    - ジョブが異常終了すること
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        csv_to_tsv_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True,
                    "line_ending": "\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_s3_retrieval_retry_exceeded_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(csv_to_tsv_config, f)

        # テスト用の一時CSVファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            temp_csv.write('id,name,value\n1,"test1",100\n2,"test2",200')
            temp_csv_path = temp_csv.name

        # S3クライアントのモック化
        mock_s3_client = MagicMock()

        # ファイル取得処理の状態管理
        retrieval_state = {
            "retry_count": 0,
            "retrieval_successful": False,
        }

        # get_objectのモック化
        def mock_get_object(**kwargs):
            # リトライ回数を増やす
            retrieval_state["retry_count"] += 1

            # 全リトライ回数分失敗させる（環境変数のS3_RETRY_LIMITに基づく）
            error_response = {
                "Error": {"Code": "InternalError", "Message": "File Retrieval Failed"}
            }
            raise botocore.exceptions.ClientError(error_response, "GetObject")

        # モックメソッドの設定
        mock_s3_client.get_object.side_effect = mock_get_object

        # 例外クラスの追加
        mock_s3_client.exceptions = type(
            "Exceptions", (), {"NoSuchKey": botocore.exceptions.ClientError}
        )

        # モックの置き換え
        original_client = boto3.client

        def mock_boto3_client(service, **kwargs):
            if service == "s3":
                return mock_s3_client
            return original_client(service, **kwargs)

        monkeypatch.setattr("boto3.client", mock_boto3_client)

# find_s3_prefix関数のモック化
        def mock_find_s3_prefix(aws_s3_dir, bucket_name):
            """find_s3_prefix関数のモック - テスト用の固定値を返す"""
            return (aws_s3_dir + "/", "")  # プレフィックスと空の日付文字列を返す

        # glue_job_convert_formatモジュール内でインポートされた関数をモック化
        monkeypatch.setattr("source.glue_job_convert_format.find_s3_prefix", mock_find_s3_prefix)
        # 環境変数のモック (リトライ回数を設定)
        monkeypatch.setenv("S3_RETRY_LIMIT", "3")
        monkeypatch.setenv("S3_RETRY_INTERVAL", "1")

        # 入力パラメータの設定
        input_file_name = "retrieval_retry_exceeded_source.csv"
        output_file_name = "retrieval_retry_exceeded_output.tsv"

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_s3_retrieval_retry_exceeded_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行（異常終了を期待）
        with pytest.raises(botocore.exceptions.ClientError) as exc_info:
            main()

        # エラーコードの検証
        assert exc_info.value.response['Error']['Code'] == 'InternalError'

        # キャプチャされた出力
        captured = capsys.readouterr()

        # リトライログの検証（指定回数分のリトライログを確認）
        retry_logs = [
            line
            for line in captured.out.split("\n")
            if "[INFO][JN_CP001-DF01_001][I_job_convert_format_003]処理をリトライしました。(処理名=S3ファイル取得)"
            in line
        ]
        assert (
            len(retry_logs) == 2
        ), f"リトライログが2回出力されていません。出力されたログ: {retry_logs}"

        # get_objectが3回呼ばれたことを確認
        assert (
            mock_s3_client.get_object.call_count == 3
        ), "get_objectの呼び出し回数が3回ではありません"

        # 異常ログの確認
        error_logs = [
            line
            for line in captured.out.split("\n")
            if "[ERROR][JN_CP001-DF01_001][E_job_convert_format_002]処理で異常が発生しました。(処理名=S3ファイル取得)"
            in line
        ]
        assert len(error_logs) > 0, "ファイル取得処理の異常ログが出力されていません"

        # 正常終了ログが出力されていないことを確認
        success_logs = [
            line
            for line in captured.out.split("\n")
            if "ジョブが正常終了しました" in line
        ]
        assert len(success_logs) == 0, "正常終了ログが出力されています"

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


def test_s3_file_retrieval_retry_success(monkeypatch, mock_aws, capsys):
    """
    TC-004: S3ファイル取得のリトライ後成功

    テスト内容:
    - S3のget_objectメソッドを2回失敗させ、3回目に成功させる
    - リトライ処理が行われること
    - リトライログが出力されること
    - 正常終了ログが出力されること
    - ジョブが正常終了すること
    """
    temp_csv_path = None
    config_path = None
    try:
        # ETL設定ファイルの作成
        csv_to_tsv_config = {
            "common": {"format": "csv2tsv", "encoding": "utf-8"},
            "input": {
                "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
            },
            "output": {
                "tsv_options": {
                    "delimiter": "\t",
                    "quote_char": "",
                    "has_header": True,
                    "line_ending": "\n",
                }
            },
        }

        config_path = get_test_resource_path(
            "config/converter/csv2tsv", "test_s3_retrieval_retry_success_config.yaml"
        )
        os.makedirs(config_path.parent, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(csv_to_tsv_config, f)

        # テスト用の一時CSVファイルを作成
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".csv", newline=""
        ) as temp_csv:
            temp_csv.write('id,name,value\n1,"test1",100\n2,"test2",200')
            temp_csv_path = temp_csv.name

        # S3クライアントのモック化
        mock_s3_client = MagicMock()

        # テストデータの準備
        sample_data = b'id,name,value\n1,"test1",100\n2,"test2",200'

        # ファイル取得処理の状態管理
        retrieval_state = {
            "retry_count": 0,
            "retrieval_successful": False,
            "retrieved_data": None,
        }

        # get_objectのモック化
        def mock_get_object(**kwargs):
            # リトライ回数を増やす
            retrieval_state["retry_count"] += 1

            # 最初の2回は失敗、3回目は成功
            if retrieval_state["retry_count"] <= 2:
                error_response = {
                    "Error": {
                        "Code": "InternalError",
                        "Message": "File Retrieval Failed",
                    }
                }
                raise botocore.exceptions.ClientError(error_response, "GetObject")

            # 3回目は成功
            retrieval_state["retrieval_successful"] = True
            retrieval_state["retrieved_data"] = sample_data

            mock_body = MagicMock()
            mock_body.read.return_value = sample_data
            return {"Body": mock_body}

        # モックメソッドの設定
        mock_s3_client.get_object.side_effect = mock_get_object

        # 例外クラスの追加
        mock_s3_client.exceptions = type(
            "Exceptions", (), {"NoSuchKey": botocore.exceptions.ClientError}
        )

        # モックの置き換え
        original_client = boto3.client

        def mock_boto3_client(service, **kwargs):
            if service == "s3":
                return mock_s3_client
            return original_client(service, **kwargs)

        monkeypatch.setattr("boto3.client", mock_boto3_client)

# find_s3_prefix関数のモック化
        def mock_find_s3_prefix(aws_s3_dir, bucket_name):
            """find_s3_prefix関数のモック - テスト用の固定値を返す"""
            return (aws_s3_dir + "/", "")  # プレフィックスと空の日付文字列を返す

        # glue_job_convert_formatモジュール内でインポートされた関数をモック化
        monkeypatch.setattr("source.glue_job_convert_format.find_s3_prefix", mock_find_s3_prefix)
        # 環境変数のモック (リトライ回数を設定)
        monkeypatch.setenv("S3_RETRY_LIMIT", "3")
        monkeypatch.setenv("S3_RETRY_INTERVAL", "1")

        # 入力パラメータの設定
        input_file_name = "retrieval_retry_success_source.csv"
        output_file_name = "retrieval_retry_success_output.tsv"

        # テストパラメータの設定
        test_args = [
            "glue_job_convert_format.py",
            "--etl_yaml_file_id",
            "csv2tsv/test_s3_retrieval_retry_success_config",
            "--input_file_dir",
            "test/input",
            "--output_file_dir",
            "test/output",
            "--input_file_name",
            input_file_name,
            "--output_file_name",
            output_file_name,
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # リトライログの検証（2回のリトライログを確認）
        retry_logs = [
            line
            for line in captured.out.split("\n")
            if "[INFO][JN_CP001-DF01_001][I_job_convert_format_003]処理をリトライしました。(処理名=S3ファイル取得)"
            in line
        ]
        assert (
            len(retry_logs) == 2
        ), f"リトライログが2回出力されていません。出力されたログ: {retry_logs}"

        # get_objectが3回呼ばれたことを確認
        assert (
            mock_s3_client.get_object.call_count == 3
        ), "get_objectの呼び出し回数が3回ではありません"

        # ファイル取得が最終的に成功したことを確認
        assert retrieval_state[
            "retrieval_successful"
        ], "ファイル取得が最終的に成功していません"
        assert (
            retrieval_state["retrieved_data"] is not None
        ), "取得したデータが存在しません"

        # 正常終了ログの確認
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_convert_format_002]ジョブが正常終了しました。(ファイル名={input_file_name})"
            in captured.out
        ), "正常終了ログが出力されていません"

        # 異常ログが出力されていないことを確認
        error_logs = [line for line in captured.out.split("\n") if "[ERROR]" in line]
        assert (
            len(error_logs) == 0
        ), f"異常ログが出力されています。出力されたエラーログ: {error_logs}"

    finally:
        # テスト用ファイルの削除
        cleanup_test_files([temp_csv_path, config_path])


if __name__ == "__main__":
    pytest.main(["-v", __file__])
