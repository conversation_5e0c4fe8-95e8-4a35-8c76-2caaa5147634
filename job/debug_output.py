#!/usr/bin/env python3
"""
実際の出力内容を確認するためのデバッグスクリプト
"""

# 実際の出力（テストから取得）
actual_output = b'ID\t\x8f\xa4\x95i\x96\xbc\t\x89\xbf\x8ai\r\n1\t\x8f\xa4\x95iA,\x93\xc1\x95\xca\x94\xc5\t100\r\n2\t\x8f\xa4\x95iB""\x8c\xc0\x92\xe8""\t200'

# 期待される出力
expected_output = b'ID\t\x8f\xa4\x95i\x96\xbc\t\x89\xbf\x8ai\r\n1\t"\x8f\xa4\x95iA,\x93\xc1\x95\xca\x94\xc5"\t100\r\n2\t"\x8f\xa4\x95iB""\x8c\xc0\x92\xe8"""\t200'

print("=== 実際の出力 ===")
try:
    actual_str = actual_output.decode('shift_jis')
    print(repr(actual_str))
    print("デコード結果:")
    print(actual_str)
except Exception as e:
    print(f"デコードエラー: {e}")

print("\n=== 期待される出力 ===")
try:
    expected_str = expected_output.decode('shift_jis')
    print(repr(expected_str))
    print("デコード結果:")
    print(expected_str)
except Exception as e:
    print(f"デコードエラー: {e}")

print("\n=== 差分分析 ===")
print(f"実際の出力長: {len(actual_output)}")
print(f"期待される出力長: {len(expected_output)}")

# バイト単位での比較
for i, (a, e) in enumerate(zip(actual_output, expected_output)):
    if a != e:
        print(f"位置 {i}: 実際={a:02x}({chr(a) if 32 <= a <= 126 else '?'}) vs 期待={e:02x}({chr(e) if 32 <= e <= 126 else '?'})")
        break
