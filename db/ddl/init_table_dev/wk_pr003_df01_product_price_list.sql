CREATE TABLE wk_pr003_df01_product_price_list (
    product_id VARCHAR(16) NOT NULL,
    product_div VARCHAR(2),
    amount INTEGER,
    oms_delete_flg NUMERIC(1) DEFAULT 0,
    split_num NUMERIC(2,0),
    PRIMARY KEY (product_id)
);
COMMENT ON TABLE wk_pr003_df01_product_price_list IS '商品連携_価格情報ワークテーブル';
COMMENT ON COLUMN wk_pr003_df01_product_price_list.product_id IS '通販商品番号';
COMMENT ON COLUMN wk_pr003_df01_product_price_list.product_div IS '商品区分';
COMMENT ON COLUMN wk_pr003_df01_product_price_list.amount IS '金額';
COMMENT ON COLUMN wk_pr003_df01_product_price_list.oms_delete_flg IS 'OMS論理削除フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_price_list.split_num IS '分割単位';
