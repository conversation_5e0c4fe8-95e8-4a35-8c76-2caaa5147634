CREATE TABLE wk_pr003_df01_product_linkage_work (
    mdm_integration_management_cd BIGINT,
    mail_order_product_cd VARCHAR(16) NOT NULL,
    web_product_name VARCHAR(200),
    tax_class_id INTEGER,
    representative_product_cd VARCHAR(10),
    registration_name VARCHAR(200),
    contents VARCHAR(6),
    core_department VARCHAR(2),
    dep VARCHAR(6),
    jan <PERSON>(13),
    product_segment VARCHAR(5),
    business_segment VARCHAR(5),
    product_cat VARCHAR(5),
    lgroup VARCHAR(2),
    mgroup VARCHAR(2),
    sgroup VARCHAR(2),
    dgroup VARCHAR(2),
    product_type VARCHAR(2),
    web VARCHAR(1),
    order_per_order_max INTEGER,
    weight NUMERIC(7,2),
    color_name VARCHAR(40),
    color_cd VARCHAR(3),
    size_name VARCHAR(40),
    size_cd VARCHAR(3),
    mail_delivery_flg VARCHAR(1),
    nekoposu_volume_rate INTEGER,
    outside_home_receive_service_flg VARCHAR(1),
    outside_home_volume_rate INTEGER,
    company_sales_buy_flg VARCHAR(1),
    set_composition_flg VARCHAR(1),
    before_renewal_product_no VARCHAR(200),
    shape_name VARCHAR(40),
    shape_cd VARCHAR(3),
    season VARCHAR(15),
    use_point_cnt INTEGER,
    sales_channel_1_sale_start_date TIMESTAMP(6),
    sales_channel_1_sale_end_date TIMESTAMP(6),
    product_series VARCHAR(5),
    set_product_flg VARCHAR(1),
    preferential_product_flg VARCHAR(1),
    txinventoryproductid VARCHAR(10),
    buttobi_subsc_bundle_yn VARCHAR(1),
    core_product_name VARCHAR(200),
    data_div VARCHAR(2),
    period_set_sales_channel_1 VARCHAR(2),
    period_set_sales_channel_2 VARCHAR(2),
    period_set_sales_channel_3 VARCHAR(2),
    split_num NUMERIC(2,0),
    PRIMARY KEY (mail_order_product_cd)
);
COMMENT ON TABLE wk_pr003_df01_product_linkage_work IS '商品連携_商品情報ワークテーブル';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.mdm_integration_management_cd IS 'MDM統合管理コード';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.mail_order_product_cd IS '通販商品番号';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.web_product_name IS 'ＷＥＢ用商品名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.tax_class_id IS '税区分ID';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.representative_product_cd IS '代表商品コード';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.registration_name IS '登録名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.contents IS 'コンテンツ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.core_department IS '基幹部門';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.dep IS '担当部署';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.jan IS 'JAN';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.product_segment IS '商品セグメント';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.business_segment IS '事業セグメント';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.product_cat IS '商品分類';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.lgroup IS '大分類';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.mgroup IS '中分類';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.sgroup IS '小分類';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.dgroup IS '細分類';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.product_type IS '商品種別';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.web IS 'WEBサイト';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.order_per_order_max IS '注文毎注文上限数';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.weight IS '重さ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.color_name IS 'カラー名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.color_cd IS 'カラーコード';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.size_name IS 'サイズ名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.size_cd IS 'サイズコード';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.mail_delivery_flg IS 'メール便フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.nekoposu_volume_rate IS 'ネコポス体積率';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.outside_home_receive_service_flg IS '自宅外受取フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.outside_home_volume_rate IS '自宅外受取体積率';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.company_sales_buy_flg IS '社販購入可';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.set_composition_flg IS 'セット構成品フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.before_renewal_product_no IS 'リニューアル前商品番号';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.shape_name IS '形状名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.shape_cd IS '形状コード';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.season IS 'シーズン';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.use_point_cnt IS '利用ポイント数';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.sales_channel_1_sale_start_date IS '販路１販売開始日時';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.sales_channel_1_sale_end_date IS '販路１販売終了日時';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.product_series IS '商品シリーズ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.set_product_flg IS 'セット商品フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.preferential_product_flg IS '優待商品フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.txinventoryproductid IS '在庫商品ID';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.buttobi_subsc_bundle_yn IS 'ぶっとび定期便同梱可否';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.core_product_name IS '基幹商品名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.data_div IS 'データ区分';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.period_set_sales_channel_1 IS '期間設定販路1';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.period_set_sales_channel_2 IS '期間設定販路2';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.period_set_sales_channel_3 IS '期間設定販路3';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.split_num IS '分割単位';