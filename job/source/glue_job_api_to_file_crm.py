# スクリプト先頭
from source.glue_deps_handler import setup_glue_dependencies

# 依存関係のセットアップを実行
setup_glue_dependencies()

# APIレスポンスデータのファイル化
import os
import traceback
import boto3
import json
import requests
import pandas as pd
import csv
import time

from source.common import initialize_env, get_job_params, retry_function, str_to_bool
from source.glue_logger import Glue<PERSON>ogger
from source.common_util import load_sql_config
from source.db_connector import DbConnector
from typing import Dict, Any
from datetime import datetime
from pytz import timezone
from zoneinfo import ZoneInfo


class GlueJobApiToFileCrm:
    """APIレスポンスデータのファイル化ジョブ"""

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id
        self.logger = GlueLogger(jobnet_id)
        self.s3_client = boto3.client("s3")
        self.api_retry_limit = 3
        self.api_retry_interval = 1.0
        self.api_retryable_status_codes = [500, 502, 503, 504]
        self.sync_datetime = None

    def connect_db(self, secret_name: str) -> DbConnector:
        """
        データベースに接続
        Args:
            secret_name: シークレット名
        Returns:
            DbConnector: DB接続オブジェクト
        """
        try:
            self.db_connector = DbConnector(self.logger, secret_id=secret_name)
            self.db_connector.connect()
            return self.db_connector
        except Exception as e:
            self.logger.error_common(f"DB接続エラー: {str(e)}")
            raise

    def get_timestamp(self):
        """
        更新済みタイムスタンプ取得
        """

        if self.diff_flag:
            sql_params = {
                "job_schedule_id": self.jobnet_id,
                "file_name": self.file_id,
            }
            result = self.db_connector.exec(
                load_sql_config("sql_common_select_001.sql"), values=sql_params
            )
            row = result.fetchone()
            self.logger.debug(f"last_sync query result: {row}")

            if row and row[0]:
                self.logger.debug(f"前回同期タイムスタンプ: {row[0]}")
                self.sync_datetime = row[0]
            else:
                # タイムスタンプが取得できない場合は、デフォルト値を設定
                default_time = datetime(1900, 1, 1, 0, 0, 0)
                self.logger.debug(
                    f"前回同期タイムスタンプがありません。デフォルト値を使用します。(default_time={default_time})"
                )
                self.sync_datetime = default_time

            self.now_datetime = datetime.now()

    def get_request_param(self, param_str: str) -> str:
        """
        各ジョブネットからリクエストパラメータを取得するための処理分岐
        Args:
            para_str:リクエストパラメータ(置換前)
        Returns:
            str: リクエストパラメータ(置換後)
        """
        if self.diff_flag:
            # 取得したタイムスタンプをJTCとして扱い、それをUTCに変換
            last_timestamp = (
                timezone("Asia/Tokyo")
                .localize(self.sync_datetime)
                .astimezone(ZoneInfo("UTC"))
                # .isoformat()
            )
            current_timestamp = (
                timezone("Asia/Tokyo")
                .localize(self.now_datetime)
                .astimezone(ZoneInfo("UTC"))
                # .isoformat()
            )

            last_timestamp = last_timestamp.strftime("%Y-%m-%dT%H:%M:%SZ")
            current_timestamp = current_timestamp.strftime("%Y-%m-%dT%H:%M:%SZ")

            param_str = str(param_str).replace("__LAST_TIMESTAMP__", last_timestamp)
            param_str = str(param_str).replace(
                "__CURRENT_TIMESTAMP__", current_timestamp
            )

        # json→dictに変換
        # param_str = json.loads(param_str)

        return param_str

    def get_api_info(self):
        """
        シークレットマネージャからAPI接続先情報を取得
        """

        # API接続情報格納secret_id
        self.secret_id = self.api_secret_name
        # secret_manager クライアント
        self.secrets_client = boto3.client("secretsmanager")

        response = self.secrets_client.get_secret_value(SecretId=self.secret_id)
        secret_string = response["SecretString"]
        self.api_info = json.loads(secret_string)

        # Extract the required information
        self.host = self.api_info.get("host")
        self.client_id = self.api_info.get("client_id")
        self.client_secret = self.api_info.get("client_secret")
        self.token_endpoint = self.api_info.get("token_endpoint")
        self.info_get_endpoint = self.api_info.get("info_get_endpoint")

    def requests_get_with_retry(self, process_name, url, headers=None):
        """
        リトライ機能付きでHTTP GETリクエストを実行する

        Args:
            process_name: プロセス名
            url: リクエスト先URL
            headers: リクエストヘッダ
        Returns:
            requests.Response: レスポンスオブジェクト
        """
        retry_counts = 0

        while retry_counts <= self.api_retry_limit:
            response = requests.get(url, headers=headers)
            status_code = response.status_code

            if status_code not in self.api_retryable_status_codes:
                return response

            retry_counts += 1
            if retry_counts <= self.api_retry_limit:
                self.logger.info(
                    "I_job_api_to_file_crm_003",
                    msg_values=(process_name),
                )
                time.sleep(self.api_retry_interval)
            else:
                self.logger.error(
                    "E_job_api_to_file_crm_002",
                    msg_values=[f"{process_name}リトライ上限"],
                )
                return response

        return response

    def requests_post_with_retry(self, process_name, url, headers=None, data=None):
        """
        リトライ機能付きでHTTP POSTリクエストを実行する

        Args:
            process_name: プロセス名
            url: リクエスト先URL
            headers: リクエストヘッダ
            data: POSTリクエストデータ
        Returns:
            requests.Response: レスポンスオブジェクト
        """
        retry_counts = 0

        while retry_counts <= self.api_retry_limit:
            response = requests.post(url, headers=headers, data=data)
            status_code = response.status_code

            # リトライ不要のステータスコードの場合は早期リターン
            if status_code not in self.api_retryable_status_codes:
                return response

            retry_counts += 1
            if retry_counts <= self.api_retry_limit:
                self.logger.info(
                    "I_job_api_to_file_crm_003",
                    msg_values=(process_name),
                )
                time.sleep(self.api_retry_interval)
            else:
                self.logger.error(
                    "E_job_api_to_file_crm_002",
                    msg_values=[f"{process_name}リトライ上限"],
                )
                return response

        return response


    def get_access_token(self) -> str:
        """
        APIトークンを取得
        Returns:
            str: APIアクセストークン
        """

        self.logger.info(
            "I_job_api_to_file_crm_004",
            msg_values=("認証情報"),
        )
        response = self.requests_get_with_retry(
            "アクセストークン取得",
            self.api_info["host"]
            + self.api_info["token_endpoint"]
            + "?grant_type=client_credentials&client_id="
            + self.api_info["client_id"]
            + "&client_secret="
            + self.api_info["client_secret"]
        )
        status_code = response.status_code
        self.logger.info(
            "I_job_api_to_file_crm_005",
            msg_values=(status_code),
        )
        if status_code != 200:
            self.logger.error(
                "E_job_api_to_file_crm_002",
                msg_values=["アクセストークン取得"],
            )
            raise BaseException
        token_info = response.json()
        return token_info["access_token"]

    def execute_api(self) -> Any:
        """
        API実行
        Returns:
            Any: レスポンスデータ(json形式)
        """

        # 開始ログ
        self.logger.info(
            "I_job_api_to_file_crm_004",
            msg_values=(self.api_request_param),
        )
        # リクエストヘッダにアクセストークンをセット
        headers = {"Authorization": f"Bearer {self.token}"}
        # パラメータによってGET/POSTを切り替える
        if self.api_request_method == "GET":
            response = self.requests_get_with_retry(
                "情報取得API実行",
                self.api_info["host"] + self.api_info["info_get_endpoint"] + "?" + self.api_request_param,
                headers=headers,
            )
        elif self.api_request_method == "POST":
            headers["Content-Type"] = "application/json"
            response = self.requests_post_with_retry(
                "情報取得API実行",
                self.api_info["host"] + self.api_info["info_get_endpoint"],
                headers=headers,
                data=self.api_request_param,
            )
        else:
            self.logger.error(
                "E_job_api_to_file_crm_002",
                msg_values=("情報取得API実行"),
            )
            raise BaseException

        status_code = response.status_code
        # 終了ログ
        self.logger.info(
            "I_job_api_to_file_crm_005",
            msg_values=(status_code),
        )
        # ステータスコードが200以外の場合、エラー
        if status_code != 200:
            self.logger.error(
                "E_job_api_to_file_crm_002",
                msg_values=("情報取得API実行"),
            )
            raise BaseException
        return response.json()

    def edit_response(self) -> pd.DataFrame:
        """
        各ジョブネットにて個別に実装するための処理分岐
        Returns:
            pd.DataFrame: レスポンスのファイルデータ
        """

        # 1. JSONレスポンスを辞書に変換
        data_dict = self.response

        # レスポンス全体が配列の場合、最初の要素を処理対象の辞書とする
        if isinstance(data_dict, list):
            data_dict = data_dict[0]

        # 2.キーの中に"isSuccess"があり、値がTrue以外の場合、E_job_api_to_file_crm_004を出力して、例外を送出する。
        if data_dict.get("isSuccess", True) != True:
            self.logger.error(
                "E_job_api_to_file_crm_004",
                msg_values=(data_dict.get("errors","errors項目なし")),
            )
            raise BaseException

        # 3. レコード格納オブジェクトキーが指定されている場合、その値を比較用辞書とする
        if self.object_key:
            if self.object_key in data_dict:
                data_dict = data_dict[self.object_key]
            else:
                return pd.DataFrame()  # キーが見つからない場合は空のDataFrameを返す

        # 4.キーの中に"Status"があり、値が"SUCCESS"以外の場合、E_job_api_to_file_crm_004を出力して、例外を送出する。
        if data_dict.get("Status", "SUCCESS").upper() != "SUCCESS":
            self.logger.error(
                "E_job_api_to_file_crm_004",
                msg_values=(data_dict.get("ErrorCode","ErrorCode項目なし") + ":" + data_dict.get("Message","Message項目なし")),
            )
            raise BaseException

        # 5. 辞書のキーを比較し、レコード特定用キーと一致する値を取得
        processing_list = []
        if self.records_key in data_dict:
            processing_list = data_dict[self.records_key]
        else:
            return pd.DataFrame()  # キーが見つからない場合は空のDataFrameを返す

        if not processing_list:
            return pd.DataFrame()  # 一致するキーが見つからない場合は空のDataFrameを返す

        # 6. 処理対象リストをループし、出力対象データのリストを作成
        output_data = []
        for record in processing_list:
            output_record = {
                key: record.get(key, None) for key in (self.output_keys or [])
            }
            output_data.append(output_record)

        # 7. 出力対象データのリストをDataFrameの形式に変換
        df = pd.DataFrame(output_data)
        return df

    def to_csv(self, file_setting) -> Any:
        """
        DataFrameをcsvファイル化
        Args:
            file_setting: csv出力オプション(json形式)
        Returns:
            Any 変換されたcsvデータ
        """

        # jsonパラメータの文字列を解釈
        file_setting_dic = json.loads(file_setting)
        # 囲み文字の出力形式 (デフォルト：QUOTE_ALL)
        if "quoting" not in file_setting_dic:
            quoting = csv.QUOTE_ALL  # デフォルト値
        else:
            if file_setting_dic["quoting"] == "csv.QUOTE_ALL":
                quoting = csv.QUOTE_ALL
            elif file_setting_dic["quoting"] == "csv.QUOTE_MINIMAL":
                quoting = csv.QUOTE_MINIMAL
            elif file_setting_dic["quoting"] == "csv.QUOTE_NONE":
                quoting = csv.QUOTE_NONE
            elif file_setting_dic["quoting"] == "csv.QUOTE_NONNUMERIC":
                quoting = csv.QUOTE_NONNUMERIC
            # python 3.9では使用不可
            # elif file_setting_dic["quoting"] == "csv.QUOTE_NOTNULL":
            #     quoting = csv.QUOTE_NOTNULL
            # python 3.9では使用不可
            # elif file_setting_dic["quoting"] == "csv.QUOTE_STRINGS":
            #     quoting = csv.QUOTE_STRINGS
        file_setting_dic["quoting"] = quoting

        # quotechar
        if "quotechar" not in file_setting_dic:
            file_setting_dic["quotechar"] = "\""  # デフォルト値
        # ヘッダー出力(デフォルト：True)
        if "header" not in file_setting_dic:
            file_setting_dic["header"] = True  # デフォルト値
        else:
            file_setting_dic["header"] = file_setting_dic["header"] == "True"
        # 行名の書き出し(デフォルト：False)
        if "index" not in file_setting_dic:
            file_setting_dic["index"] = False  # デフォルト値
        else:
            file_setting_dic["index"] = file_setting_dic["index"] == "True"
        # エンコード(デフォルト：UTF-8)
        if "encoding" not in file_setting_dic:
            file_setting_dic["encoding"] = "utf-8"  # デフォルト値

        return self.df.to_csv(**file_setting_dic).encode(file_setting_dic["encoding"])

    def put_to_s3(self, file_data: str, output_path: str):
        """
        S3にファイルを配置（リトライ処理付き）
        Args:
            file_data: ファイル内容
            output_path: 出力パス(ファイル名含むフルパス)
        """

        # ファイル配置
        def put_file():
            self.s3_client.put_object(
                Body=file_data,
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=output_path,
            )

        # リトライ共通関数呼び出し
        retry_function(
            func=put_file,
            info_msg_id="I_job_api_to_file_crm_003",
            error_msg_id="E_job_api_to_file_crm_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=float(os.environ.get("S3_RETRY_INTERVAL")),
            logger=self.logger,
            process_name="S3ファイル配置",
        )

    def update_timestamp(self):
        """
        前回同期済タイムスタンプの更新
        """
        try:
            if self.diff_flag:  # diff_flagがTrueの時のみ実行
                # sync_update_later_flgが設定されている場合、一時格納用のタイムスタンプを更新し後続ジョブで日付を更新する
                if self.sync_update_later_flg == "1":
                    update_query = load_sql_config("sql_common_update_002.sql")
                else:
                    update_query = load_sql_config("sql_common_update_001.sql")

                # パラメータ設定
                params = {
                    "job_schedule_id": self.jobnet_id,
                    "file_name": self.file_id,
                    "timestamp": self.now_datetime,
                    "user": self.jobnet_id,
                }
                self.db_connector.exec(update_query, values=params)

        except Exception as e:
            self.db_connector.rollback()  # エラー時はロールバック
            self.logger.error(
                "E_job_api_to_file_crm_002",
                msg_values=("クエリ実行"),
            )
            self.logger.error_common(f"前回同期済タイムスタンプの更新エラー: {str(e)}")
            raise

    def execute(self, params: Dict[str, Any]):
        """
        メイン処理
        Args:
            params: 実行パラメータ
        """

        try:
            # 2.4.1 パラメータ取得
            self.params = get_job_params()
            self.api_secret_name = params["api_secret_name"]
            self.diff_flag = str_to_bool(params["diff_flag"])
            self.file_name = params["file_name"]
            self.output_file_dir = params["output_file_dir"]
            self.file_setting = params["file_setting"]
            self.jobnet_id = params["jobnet_id"]
            self.api_request_param = params["api_request_param"]
            self.api_request_method = params["api_request_method"]
            self.records_key = params["records_key"]
            self.output_keys = str(params["output_keys"]).split(",")
            self.object_key = None
            self.file_id = None
            self.sync_update_later_flg = params.get("sync_update_later_flg")

            # 条件付きパラメータのセット
            if self.diff_flag:
                # 差分連携の場合、ファイル識別子をセット
                self.file_id = params["file_id"]

            # 必須でないパラメータがあれば格納
            if "object_key" in params:
                self.object_key = params["object_key"]

            # 2.4.2 開始ログ出力
            self.logger.info(
                "I_job_api_to_file_crm_001",
                msg_values=(self.file_name),
            )

            self.db_connector = None
            self.db_connector = self.connect_db(params["secret_name"])
            self.db_connector.begin()

            # 2.4.3.前回同期済タイムスタンプ取得
            self.get_timestamp()

            # 差分連携フラグ、前回同期済みタイムスタンプ、出力ファイルパスをログ出力
            output_path = os.path.join(self.output_file_dir, self.file_name)
            self.logger.info("I_job_api_to_file_crm_006", (self.diff_flag, self.sync_datetime, output_path))

            # 2.4.4.リクエストパラメータ作成
            self.api_request_param = self.get_request_param(self.api_request_param)

            # シークレットマネージャからAPI接続先情報を取得
            self.get_api_info()
            # 2.4.5.アクセストークン取得
            self.token = self.get_access_token()

            # 2.4.6.情報取得API実行
            self.response = self.execute_api()

            # 2.4.7.レスポンス内容加工
            self.df = self.edit_response()

            # 2.4.8.出力ファイル形式変換
            self.file_data = self.to_csv(self.file_setting)

            # 2.4.9.S3ファイル配置
            self.put_to_s3(
                self.file_data, os.path.join(self.output_file_dir, self.file_name)
            )

            # 2.4.10.前回同期済タイムスタンプ更新
            self.update_timestamp()
            self.db_connector.commit()

            self.db_connector.commit()
            self.db_connector.close()

            # 2.4.11 終了処理
            self.logger.info("I_job_api_to_file_crm_002", msg_values=(self.file_name))

        except BaseException:
            self.db_connector.rollback()
            # 2.4.12 例外処理
            self.logger.error(
                "E_job_api_to_file_crm_003",
                msg_values=(traceback.format_exc()),
            )
            self.logger.error("E_job_api_to_file_crm_001", msg_values=(self.file_name))
            raise

        finally:
            self.db_connector.close()


def get_params() -> Dict[str, Any]:
    """
    パラメータ取得
    Returns:
    Dict[str,Any]: ジョブ起動パラメータ
    """

    # 2.4.1 パラメータ取得
    params = get_job_params()

    # 必須パラメータのチェック
    required_params = [
        "secret_name",  # API　Secrets Managerシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        "file_name",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "output_keys",  # 出力対象キー
    ]

    for param in required_params:
        if param not in params:
            raise ValueError(f"Required parameter '{param}' is missing")

    # 任意パラメータのデフォルト値設定
    params.setdefault("sync_update_later_flg", "0")  # 前回同期済タイムスタンプを全ジョブ終了後に更新するフラグ

    # 差分連携の場合
    if str_to_bool(params["diff_flag"]):
        if "file_id" not in params:
            raise ValueError("Required parameter 'sync_datetime_key' is missing")

    return params


def main():
    """
    メイン関数
    """

    try:
        # 環境変数の初期化
        initialize_env()

        # パラメータの取得
        params = get_params()

        # ジョブの実行
        job = GlueJobApiToFileCrm(params["jobnet_id"])
        job.execute(params)

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        raise e


if __name__ == "__main__":
    main()
