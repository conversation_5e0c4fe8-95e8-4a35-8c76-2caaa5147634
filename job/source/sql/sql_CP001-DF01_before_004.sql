TRUNCATE TABLE wk_cp001_df01_dynamic_customer_groups_main;
--動的顧客のキャンペーン基本情報メインクエリ
INSERT INTO wk_cp001_df01_dynamic_customer_groups_main (campaign_instructions_code, group_id, campaign_instructions_name, include_only_flg, exclude_only_flg, dynamic_customer_exist_flg)
SELECT 
  uecl.campaign_instructions_code
  ,'txGroupId' || uecl.campaign_instructions_code
  ,campaign_instructions_name
  ,CASE
    WHEN EXISTS (
      SELECT 1
      FROM campaign_order_group_view AS cog
      INNER JOIN campaign_order_view AS co ON
        cog.campaign_instructions_code = co.campaign_instructions_code
        AND cog.campaign_group_no = co.campaign_group_no
        AND cog.delete_flg = 0
        AND co.delete_flg = 0
        AND co.joken_kind1 = '1'
        AND co.joken_type = '2'
        AND co.joken_kind2 = '101'
        AND cog.campaign_instructions_code = uecl.campaign_instructions_code
    ) THEN '0' ELSE '1'
  END
  ,CASE
    WHEN EXISTS (
      SELECT 1
      FROM campaign_order_group_view AS cog
      INNER JOIN campaign_order_view AS co ON
        cog.campaign_instructions_code = co.campaign_instructions_code
        AND cog.campaign_group_no = co.campaign_group_no
        AND cog.delete_flg = 0
        AND co.delete_flg = 0
        AND co.joken_kind1 = '1'
        AND co.joken_type = '1'
        AND co.joken_kind2 = '101'
        AND cog.campaign_instructions_code = uecl.campaign_instructions_code
    ) THEN '0' ELSE '1'
  END
  ,dynamic_customer_exist_flg
FROM wk_CP001_DF01_updated_campaign_promotion_list AS uecl 
INNER JOIN campaign_instructions AS ci ON
  uecl.dynamic_customer_flg = '1'
  AND uecl.campaign_instructions_code = ci.campaign_instructions_code;
--動的顧客の条件グループ取得クエリ(適用条件)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT DISTINCT
      cog.campaign_instructions_code
      ,cog.campaign_group_no
    FROM campaign_order_group_view AS cog
    INNER JOIN campaign_order_view AS co ON
      cog.campaign_instructions_code = co.campaign_instructions_code
      AND cog.campaign_group_no = co.campaign_group_no
      AND cog.delete_flg = 0
      AND co.delete_flg = 0
      AND co.joken_kind1 = ''1''
      AND co.joken_type = ''1''
      AND co.joken_kind2 = ''101''
    ORDER BY cog.campaign_instructions_code, cog.campaign_group_no;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :dy_grp_inc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--動的顧客の条件取得クエリ(適用条件)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT 
      campaign_instructions_code,
      campaign_group_no,
      joken
    FROM campaign_order_view
    WHERE delete_flg = 0
      AND joken_kind1 = ''1''
      AND joken_type = ''1''
      AND joken_kind2 = ''101''
    ORDER BY campaign_instructions_code, campaign_group_no;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :dy_ord_inc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--動的顧客の条件グループ取得クエリ(除外条件)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT DISTINCT
      cog.campaign_instructions_code
      ,cog.campaign_group_no
    FROM campaign_order_group_view AS cog
    INNER JOIN campaign_order_view AS co ON
      cog.campaign_instructions_code = co.campaign_instructions_code
      AND cog.campaign_group_no = co.campaign_group_no
      AND cog.delete_flg = 0
      AND co.delete_flg = 0
      AND co.joken_kind1 = ''1''
      AND co.joken_type = ''2''
      AND co.joken_kind2 = ''101''
    ORDER BY cog.campaign_instructions_code, cog.campaign_group_no;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :dy_grp_exc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--動的顧客の条件取得クエリ(除外条件)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT 
      campaign_instructions_code,
      campaign_group_no,
      joken
    FROM campaign_order_view AS co1
    WHERE delete_flg = 0
      AND joken_kind1 = ''1''
      AND joken_type = ''2''
      AND joken_kind2 = ''101''
    ORDER BY campaign_instructions_code, campaign_group_no;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :dy_ord_exc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--分割単位の設定
-- 動的顧客
WITH temp_parallel_num AS (
  SELECT
     campaign_instructions_code
    ,MOD(ROW_NUMBER() OVER (ORDER BY campaign_instructions_code) - 1, :parallel_num) + 1 AS parallel_num
  FROM (SELECT DISTINCT campaign_instructions_code FROM wk_cp001_df01_dynamic_customer_groups_main)
)
UPDATE wk_cp001_df01_dynamic_customer_groups_main AS dcgm
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE dcgm.campaign_instructions_code = t.campaign_instructions_code;