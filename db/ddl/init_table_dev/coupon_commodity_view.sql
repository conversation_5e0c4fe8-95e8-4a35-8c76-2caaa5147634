CREATE TABLE coupon_commodity_view (
    coupon_management_code VARCHAR(16) NOT NULL,
    shop_code VARCHAR(16) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (coupon_management_code)
);
COMMENT ON TABLE coupon_commodity_view IS 'クーポン適用商品';
COMMENT ON COLUMN coupon_commodity_view.coupon_management_code IS 'クーポン管理コード';
COMMENT ON COLUMN coupon_commodity_view.shop_code IS 'ショップコード';
COMMENT ON COLUMN coupon_commodity_view.commodity_code IS '商品コード';
COMMENT ON COLUMN coupon_commodity_view.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN coupon_commodity_view.created_user IS '作成ユーザ';
COMMENT ON COLUMN coupon_commodity_view.created_datetime IS '作成日時';
COMMENT ON COLUMN coupon_commodity_view.updated_user IS '更新ユーザ';
COMMENT ON COLUMN coupon_commodity_view.updated_datetime IS '更新日時';
COMMENT ON COLUMN coupon_commodity_view.delete_flg IS '削除フラグ';
COMMENT ON COLUMN coupon_commodity_view.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN coupon_commodity_view.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN coupon_commodity_view.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN coupon_commodity_view.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN coupon_commodity_view.d_version IS 'デ連バージョン';

