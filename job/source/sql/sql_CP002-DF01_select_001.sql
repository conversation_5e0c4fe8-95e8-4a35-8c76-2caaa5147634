SELECT coupon_management_code,
    coupon_code,
    coupon_name,
    coupon_invalid_flag,
    coupon_type,
    coupon_issue_type,
    coupon_use_limit,
    coupon_use_purchase_price,
    coupon_discount_type,
    coupon_discount_rate,
    coupon_discount_price,
    coupon_start_datetime,
    coupon_end_datetime,
    coupon_limit_display_period,
    coupon_limit_display,
    coupon_description,
    coupon_message,
    coupon_kbn,
    coupon_post_in_charge,
    coupon_commodity_flag,
    marketing_channel_list,
    goods_group,
    commodity_category_code,
    commodity_series,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg
FROM coupon_view
WHERE updated_datetime > :sync_datetime
    AND updated_datetime <= :diff_base_timestamp;
