#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, List, Any, Optional
from datetime import datetime
import pandas as pd


class QueryExecutorMixin:
    """クエリ実行の責務を担当"""

    def _execute_queries(self) -> Dict[str, List[Dict]]:
        """
        全てのメインクエリの実行

        設定ファイルで定義されたメインクエリ（サブツリーの親となるクエリ）を実行します。
        例えば、キャンペーンやプロモーションの基本情報を取得するクエリが対象となります。
        サブクエリや子要素のクエリは、後続の処理で必要に応じて個別に実行されます。

        Returns:
            Dict[str, List[Dict]]: メインクエリ名をキーとする実行結果
            例：{
                "campaign_query": [...],     # キャンペーン情報
                "promotion_query": [...],    # プロモーション情報
            }
        """
        results = {}
        try:
            # パラメータの準備
            etl_config = self.config.get("etl", {})
            param_definitions = etl_config.get("parameters", {})
            query_params = self._get_query_params()
            self.logger.debug(f"準備されたパラメータ: {query_params}")

            # 親子関係の解析
            query_info = self._analyze_query_relationships()
            self.logger.debug(f"解析結果: {query_info}")

            # 親クエリのみを実行
            for query_name, query_def in etl_config.items():
                # 特殊セクションはスキップ
                if query_name in ["parameters", "mappings", "transformations"]:
                    continue

                # 親クエリの場合のみ実行
                if (isinstance(query_def, str) or isinstance(query_def, list))  and query_info.get(query_name, True):
                    self.logger.debug(f"クエリ処理: {query_name}")
                    results[query_name] = self._execute_query(query_name, query_params, None, isinstance(query_def, list))
                    self.logger.debug(f"クエリ {query_name} の実行完了")

            self.logger.debug(f"親クエリ実行完了: {len(results)}件のクエリを実行")
            return results

        except Exception as e:
            self.logger.error_common(f"親クエリ実行エラー: {str(e)}")
            raise

    def _analyze_query_relationships(self) -> Dict[str, bool]:
        """
        マッピング定義から親子関係を解析
        Returns:
            Dict[str, bool]: クエリ名をキーとする親クエリフラグ
        """
        try:
            self.logger.debug("クエリの親子関係解析開始")
            query_info = {}
            child_queries = set()
            mappings = self.config.get("etl", {}).get("mappings", [])

            def process_mapping(mapping: Dict) -> None:
                """マッピング定義を再帰的に処理"""
                # MainQueryを探索
                main_query = self._find_main_query(mapping)
                if main_query:
                    query_info[main_query] = True

                if "child_elements" in mapping:
                    for child in mapping["child_elements"]:
                        # SubQueryを収集
                        sub_query = child.get("sub_query")
                        if sub_query:
                            child_queries.add(sub_query)
                        process_mapping(child)

            # マッピング定義を処理
            for mapping in mappings:
                process_mapping(mapping)

            # 子として使用されているクエリの親フラグをFalseに設定
            for child_query in child_queries:
                query_info[child_query] = False

            return query_info

        except Exception as e:
            self.logger.error_common(f"クエリの親子関係解析エラー: {str(e)}")
            raise

    def _get_query_params(self) -> Dict[str, Any]:
        """
        パラメータの準備
        Returns:
            Dict[str, Any]: 準備されたパラメータ
        """
        try:
            parameter_definitions = self.config.get("etl", {}).get("parameters", {})
            params = {}

            for param_name, param_config in parameter_definitions.items():
                param_type = param_config.get("type")
                default_value = param_config.get("default")
                is_required = param_config.get("required", False)

                value = getattr(self, param_name, None)

                if value is not None:
                    params[param_name] = value
                elif default_value is not None:
                    params[param_name] = default_value
                elif is_required:
                    raise ValueError(f"必須パラメータが未設定です: {param_name}")

                if param_type == "datetime" and isinstance(params.get(param_name), str):
                    params[param_name] = datetime.fromisoformat(params[param_name])

            return params

        except Exception as e:
            self.logger.error_common(f"パラメータ準備エラー: {str(e)}")
            raise

    def _execute_query(
        self
        ,query_name: str
        ,params: Dict
        ,parent_record: Optional[Dict] = None
        ,sql_list_flag: bool = False
    ) -> List[Dict]:
        """
        個別のクエリを実行
        Args:
            query_name: クエリ名
            params: 基本パラメータ
            parent_record: 親レコードの情報（子クエリ実行時に使用）
            sql_list_flag: SQLをリストで実行する場合True
        Returns:
            List[Dict]: クエリ実行結果
        """
        try:
            # クエリパラメータの準備
            query_params = params.copy()

            # 親レコードの情報をパラメータに追加
            if parent_record:
                query_params.update(parent_record)

            self.logger.debug(f"パラメータ情報: {query_params}")
            if sql_list_flag:
                query_list = self.config["etl"][query_name]
                for i, query in enumerate(query_list):
                    if i != len(query_list) - 1:
                        
                        self.db_connection.exec(query, query_params)
                        # SELECT以外はCOMMITを挟む
                        self.db_connection.commit()
                        self.db_connection.begin()
                    else:
                        # results = (row for row in self.db_connection.exec(query, query_params))
                        results = list(self.db_connection.exec(query, query_params))
            else:
                # results = (row for row in self.db_connection.exec(self.config["etl"][query_name], query_params))
                results = list(self.db_connection.exec(self.config["etl"][query_name], query_params))
            # self.logger.debug(f"クエリ実行: {query_name}")
            # self.logger.debug(f"クエリパラメータ: {query_params}")
            # self.logger.debug(f"クエリ実行結果: {results}")
            return results

        except Exception as e:
            self.logger.error_common(
                f"クエリ実行エラー: query={query_name}, error={str(e)}"
            )
            raise

    def _find_main_query(self, mapping: Dict[str, Any]) -> Optional[str]:
        """
        マッピング定義からMainQueryを探索
        SubQueryは含まない

        Args:
            mapping: マッピング定義
        Returns:
            str: 見つかったMainQuery名、なければNone
        """
        try:
            # MainQueryのみを探索
            query = mapping.get("query")
            if query:
                return query

            if "child_elements" in mapping:
                for child in mapping["child_elements"]:
                    child_query = self._find_main_query(child)
                    if child_query:
                        return child_query
            return None

        except Exception as e:
            self.logger.error_common(f"MainQuery探索エラー: {str(e)}")
            raise

    def _get_query(self, query_name: str) -> str:
        """
        クエリ名に対応するSQLを取得
        Args:
            query_name: クエリ名
        Returns:
            str: SQL文
        """
        try:
            return self.config["etl"][query_name]
        except KeyError:
            self.logger.error_common(f"クエリが未定義です: {query_name}")
            raise ValueError(f"クエリが未定義です: {query_name}")

    def _execute_sub_query(
        self, query_name: str, params: Dict, parent_record: Dict
    ) -> List[Dict]:
        """
        サブクエリを実行
        Args:
            query_name: サブクエリ名
            params: 基本パラメータ
            parent_record: 親レコードの情報（必須）
        Returns:
            List[Dict]: クエリ実行結果
        """
        try:
            if not parent_record:
                raise ValueError("サブクエリの実行には親レコードが必要です")

            if hasattr(self,'df_sub_querys'):
                return self._create_sub_query_with_csv(query_name, parent_record)
            else:
                return self._execute_query(query_name, params, parent_record)

        except Exception as e:
            self.logger.error_common(
                f"サブクエリ実行エラー: query={query_name}, error={str(e)}"
            )
            raise

    def _create_sub_query_with_csv(self, query_name, parent_record)-> List[Dict]:
        if query_name in list(self.df_sub_querys.keys()):
            df_sub_query = self.df_sub_querys[query_name]
            parent_column = self.parent_column
        else:
            df_sub_query = self.df_sub_sub_querys[query_name]
            parent_column = self.sub_parent_column
        sub_query_dict_list = []
        if "," in parent_column:
            parant_column_list = parent_column.split(",")
            group_key_tuple = tuple([parent_record[key] for key in parant_column_list])
            if group_key_tuple in df_sub_query.groups.keys():
                sub_query_group = df_sub_query.get_group(group_key_tuple)
                for index, data in sub_query_group.iterrows():
                    sub_query_dict_list.append(dict(data))
        else:
            group_key = parent_record[parent_column]
            if group_key in df_sub_query.groups.keys():
                sub_query_group = df_sub_query.get_group(group_key)
                for index, data in sub_query_group.iterrows():
                    sub_query_dict_list.append(dict(data))
        return sub_query_dict_list