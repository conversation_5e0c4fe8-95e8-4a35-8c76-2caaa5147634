AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Campaign Data Integration Jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_CP001-DF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for Campaign Data Integration JN_CP001-DF01_001",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "parallel_csv_generation"
            },
            "parallel_csv_generation": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "1_job_db_to_file",
                  "States": {
                    "1_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_001",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_instructions_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_001",
                          "file_id": "campaign_instructions.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_job_db_to_file",
                  "States": {
                    "2_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_002",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_customer_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_002",
                          "file_id": "campaign_customer.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "3_job_db_to_file",
                  "States": {
                    "3_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_003",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_order_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_003",
                          "file_id": "campaign_order.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "4_job_db_to_file",
                  "States": {
                    "4_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_004",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_order_group_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_004",
                          "file_id": "campaign_order_group.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "5_job_db_to_file",
                  "States": {
                    "5_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_005",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_promotion_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_005",
                          "file_id": "campaign_promotion.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "6_job_db_to_file",
                  "States": {
                    "6_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_006",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_instructions_commodity_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_006",
                          "file_id": "campaign_instructions_commodity.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "7_job_db_to_file",
                  "States": {
                    "7_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_007",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_combi_limit_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_007",
                          "file_id": "campaign_combi_limit.csv"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "PrepareDateParameters"
            },
            "PrepareDateParameters": {
              "Type": "Pass",
              "InputPath": "$[0]",
              "Parameters": {
                "timestamp.$": "States.ArrayGetItem(States.StringSplit(States.ArrayGetItem(States.StringSplit($.Arguments.file_name, '_'), 2), '.'), 0)"
              },
              "Next": "parallel_db_import"
            },
            "parallel_db_import": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "8_job_internal_db_import",
                  "States": {
                    "8_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_instructions_{}.csv', $.timestamp)",
                          "import_table": "campaign_instructions_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_001",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "9_job_internal_db_import",
                  "States": {
                    "9_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_customer_{}.csv', $.timestamp)",
                          "import_table": "campaign_customer_view_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_002",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "10_job_internal_db_import",
                  "States": {
                    "10_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_order_{}.csv', $.timestamp)",
                          "import_table": "campaign_order_view_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_003",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "11_job_internal_db_import",
                  "States": {
                    "11_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_order_group_{}.csv', $.timestamp)",
                          "import_table": "campaign_order_group_view_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_004",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "12_job_internal_db_import",
                  "States": {
                    "12_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_promotion_{}.csv', $.timestamp)",
                          "import_table": "campaign_promotion_view_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_005",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "13_job_internal_db_import",
                  "States": {
                    "13_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_instructions_commodity_{}.csv', $.timestamp)",
                          "import_table": "campaign_instructions_commodity_view_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_006",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "14_job_internal_db_import",
                  "States": {
                    "14_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_combi_limit_{}.csv', $.timestamp)",
                          "import_table": "campaign_combi_limit_view_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_007",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "GetParallelNum"
            },
            "GetParallelNum": {
              "Type": "Pass",
              "Next": "15_job_execute_sql",
              "QueryLanguage": "JSONata",
              "Assign": {
                "promotions_parallel_num": 1,
                "pricebooks_parallel_num": 1,
                "static_parallel_num": 2,
                "dynamic_parallel_num": 1
              }
            },
            "15_job_execute_sql": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_CP001-DF01_001",
                  "file_id": "txCampaignPromotions.xml",
                  "sql_info": "{% '{\"sql_info\": [{\"query_id\": \"sql_CP001-DF01_before_001\", \"params\": {\"parallel_num\":\"' & $promotions_parallel_num & '\", \"pro_order_object_path\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_pro_order.csv\", \"pro_single_disc_object_path\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_pro_single.csv\", \"pro_multi_disc_object_path\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_pro_multi.csv\"}}]}' %}"
                }
              },
              "Next": "parallel_execute_sql"
            },
            "parallel_execute_sql": {
              "QueryLanguage": "JSONata",
              "Type": "Parallel",
              "Next": "parallel_lambda_parallel_num",
              "Branches": [
                {
                  "StartAt": "16_job_execute_sql",
                  "States": {
                    "16_job_execute_sql": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_execute_sql",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_id": "txCampaignPricebooks.xml",
                          "sql_info": "{% '{\"sql_info\": [{\"query_id\": \"sql_CP001-DF01_before_002\", \"params\": {\"parallel_num\":\"' & $pricebooks_parallel_num & '\", \"pri_single_object_path\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_pri_single.csv\", \"pri_multi_object_path\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_pri_multi.csv\"}}]}' %}"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "17_job_execute_sql",
                  "States": {
                    "17_job_execute_sql": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_execute_sql",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_id": "txStaticCustomerGroups.xml",
                          "sql_info": "{% '{\"sql_info\": [{\"query_id\": \"sql_CP001-DF01_before_003\", \"params\": {\"parallel_num\":\"' & $static_parallel_num & '\"}}]}' %}"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "18_job_execute_sql",
                  "States": {
                    "18_job_execute_sql": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_execute_sql",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_id": "txDynamicCustomerGroups.xml",
                          "sql_info": "{% '{\"sql_info\": [{\"query_id\": \"sql_CP001-DF01_before_004\", \"params\": {\"parallel_num\":\"' & $dynamic_parallel_num & '\", \"dy_grp_inc_object_path\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_dy_grp_inc.csv\", \"dy_ord_inc_object_path\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_dy_ord_inc.csv\", \"dy_grp_exc_object_path\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_dy_grp_exc.csv\", \"dy_ord_exc_object_path\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_dy_ord_exc.csv\"}}]}' %}"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ]
            },
            "parallel_lambda_parallel_num": {
              "QueryLanguage": "JSONata",
              "Type": "Parallel",
              "Next": "parallel_xml_generation",
              "Branches": [
                {
                  "StartAt": "19_DLPF_RETURN_PARALLEL_NUM_ARRAY_PROMOTIONS",
                  "States": {
                    "19_DLPF_RETURN_PARALLEL_NUM_ARRAY_PROMOTIONS": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Arguments": {
                        "Payload": {
                          "parallel_num": "{% $promotions_parallel_num %}"
                        },
                        "FunctionName": "arn:aws:lambda:ap-northeast-1:${AccountId}:function:DLPF_RETURN_PARALLEL_NUM_ARRAY:$LATEST"
                      },
                      "Output": {
                        "promotions_parallel_num_array": "{% $states.result.Payload %}"
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "20_DLPF_RETURN_PARALLEL_NUM_ARRAY_PRICEBOOKS",
                  "States": {
                    "20_DLPF_RETURN_PARALLEL_NUM_ARRAY_PRICEBOOKS": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Arguments": {
                        "Payload": {
                          "parallel_num": "{% $pricebooks_parallel_num %}"
                        },
                        "FunctionName": "arn:aws:lambda:ap-northeast-1:${AccountId}:function:DLPF_RETURN_PARALLEL_NUM_ARRAY:$LATEST"
                      },
                      "Output": {
                        "pricebooks_parallel_num_array": "{% $states.result.Payload %}"
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "21_DLPF_RETURN_PARALLEL_NUM_ARRAY_STATIC",
                  "States": {
                    "21_DLPF_RETURN_PARALLEL_NUM_ARRAY_STATIC": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Arguments": {
                        "Payload": {
                          "parallel_num": "{% $static_parallel_num %}"
                        },
                        "FunctionName": "arn:aws:lambda:ap-northeast-1:${AccountId}:function:DLPF_RETURN_PARALLEL_NUM_ARRAY:$LATEST"
                      },
                      "Output": {
                        "static_parallel_num_array": "{% $states.result.Payload %}"
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "22_DLPF_RETURN_PARALLEL_NUM_ARRAY_DYNAMIC",
                  "States": {
                    "22_DLPF_RETURN_PARALLEL_NUM_ARRAY_DYNAMIC": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Arguments": {
                        "Payload": {
                          "parallel_num": "{% $dynamic_parallel_num %}"
                        },
                        "FunctionName": "arn:aws:lambda:ap-northeast-1:${AccountId}:function:DLPF_RETURN_PARALLEL_NUM_ARRAY:$LATEST"
                      },
                      "Output": {
                        "dynamic_parallel_num_array": "{% $states.result.Payload %}"
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Assign": {
                "promotions_parallel_num_array": "{% $states.result[0].promotions_parallel_num_array %}",
                "pricebooks_parallel_num_array": "{% $states.result[1].pricebooks_parallel_num_array %}",
                "static_parallel_num_array": "{% $states.result[2].static_parallel_num_array %}",
                "dynamic_parallel_num_array": "{% $states.result[3].dynamic_parallel_num_array %}"
              }
            },
            "parallel_xml_generation": {
              "QueryLanguage": "JSONata",
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "XML_Map_Promotions",
                  "States": {
                    "XML_Map_Promotions": {
                      "QueryLanguage": "JSONata",
                      "Type": "Map",
                      "ItemProcessor": {
                        "ProcessorConfig": {
                          "Mode": "INLINE"
                        },
                        "StartAt": "23_job_db_to_file",
                        "States": {
                          "23_job_db_to_file": {
                            "QueryLanguage": "JSONata",
                            "Type": "Task",
                            "Resource": "arn:aws:states:::glue:startJobRun.sync",
                            "Arguments": {
                              "JobName": "job_db_to_file",
                              "Arguments": {
                                "secret_name": "DLPF_DB_INFO",
                                "execute_query": "etl_CP001-DF01_txCampaignPromotions",
                                "batch_size": "1000",
                                "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "file_name": "{% 'txCampaignPromotions_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                                "file_type": "xml",
                                "jobnet_id": "JN_CP001-DF01_001",
                                "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_008",
                                "file_id": "txCampaignPromotions.xml",
                                "split_num": "{% $string($states.input.split_num) %}",
                                "tmp_csv_file": "{\"campaign_order_query\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_pro_order.csv\", \"promotion_rule_single_product_query\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_pro_single.csv\", \"promotion_rule_multiple_product_query\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_pro_multi.csv\"}",
                                "parent_column": "campaign_instructions_code",
                                "sync_update_skip_flg": "1"
                              }
                            },
                            "End": true
                          }
                        }
                      },
                      "Items": "{% $promotions_parallel_num_array %}",
                      "ItemSelector": {
                        "split_num": "{% $states.context.Map.Item.Value %}"
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "XML_Map_Pricebooks",
                  "States": {
                    "XML_Map_Pricebooks": {
                      "QueryLanguage": "JSONata",
                      "Type": "Map",
                      "ItemProcessor": {
                        "ProcessorConfig": {
                          "Mode": "INLINE"
                        },
                        "StartAt": "24_job_db_to_file",
                        "States": {
                          "24_job_db_to_file": {
                            "QueryLanguage": "JSONata",
                            "Type": "Task",
                            "Resource": "arn:aws:states:::glue:startJobRun.sync",
                            "Arguments": {
                              "JobName": "job_db_to_file",
                              "Arguments": {
                                "secret_name": "DLPF_DB_INFO",
                                "execute_query": "etl_CP001-DF01_txCampaignPricebooks",
                                "batch_size": "1000",
                                "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "file_name": "{% 'txCampaignPricebooks_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                                "file_type": "xml",
                                "jobnet_id": "JN_CP001-DF01_001",
                                "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_009",
                                "file_id": "txCampaignPricebooks.xml",
                                "split_num": "{% $string($states.input.split_num) %}",
                                "tmp_csv_file": "{\"single_product_price_table_query\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_pri_single.csv\", \"multiple_products_price_table_query\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_pri_multi.csv\"}",
                                "parent_column": "campaign_instructions_code",
                                "sync_update_skip_flg": "1"
                              }
                            },
                            "End": true
                          }
                        }
                      },
                      "End": true,
                      "Items": "{% $pricebooks_parallel_num_array %}",
                      "ItemSelector": {
                        "split_num": "{% $states.context.Map.Item.Value %}"
                      }
                    }
                  }
                },
                {
                  "StartAt": "XML_Map_Static",
                  "States": {
                    "XML_Map_Static": {
                      "QueryLanguage": "JSONata",
                      "Type": "Map",
                      "ItemProcessor": {
                        "ProcessorConfig": {
                          "Mode": "INLINE"
                        },
                        "StartAt": "25_job_db_to_file",
                        "States": {
                          "25_job_db_to_file": {
                            "QueryLanguage": "JSONata",
                            "Type": "Task",
                            "Resource": "arn:aws:states:::glue:startJobRun.sync",
                            "Arguments": {
                              "JobName": "job_db_to_file",
                              "Arguments": {
                                "secret_name": "DLPF_DB_INFO",
                                "execute_query": "etl_CP001-DF01_txStaticCustomerGroups",
                                "batch_size": "1000",
                                "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "file_name": "{% 'txStaticCustomerGroups_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                                "file_type": "xml",
                                "jobnet_id": "JN_CP001-DF01_001",
                                "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_010",
                                "file_id": "txStaticCustomerGroups.xml",
                                "split_num": "{% $string($states.input.split_num) %}",
                                "sync_update_skip_flg": "1"
                              }
                            },
                            "End": true
                          }
                        }
                      },
                      "End": true,
                      "Items": "{% $static_parallel_num_array %}",
                      "ItemSelector": {
                        "split_num": "{% $states.context.Map.Item.Value %}"
                      }
                    }
                  }
                },
                {
                  "StartAt": "XML_Map_Dynamic",
                  "States": {
                    "XML_Map_Dynamic": {
                      "QueryLanguage": "JSONata",
                      "Type": "Map",
                      "ItemProcessor": {
                        "ProcessorConfig": {
                          "Mode": "INLINE"
                        },
                        "StartAt": "26_job_db_to_file",
                        "States": {
                          "26_job_db_to_file": {
                            "QueryLanguage": "JSONata",
                            "Type": "Task",
                            "Resource": "arn:aws:states:::glue:startJobRun.sync",
                            "Arguments": {
                              "JobName": "job_db_to_file",
                              "Arguments": {
                                "secret_name": "DLPF_DB_INFO",
                                "execute_query": "etl_CP001-DF01_txDynamicCustomerGroups",
                                "batch_size": "1000",
                                "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "file_name": "{% 'txDynamicCustomerGroups_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                                "file_type": "xml",
                                "jobnet_id": "JN_CP001-DF01_001",
                                "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_011",
                                "file_id": "txDynamicCustomerGroups.xml",
                                "split_num": "{% $string($states.input.split_num) %}",
                                "tmp_csv_file": "{\"condition_group_include_query\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_dy_grp_inc.csv\", \"condition_group_exclude_query\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_dy_grp_exc.csv\"}",
                                "parent_column": "campaign_instructions_code",
                                "sub_tmp_csv_file": "{\"condition_include_query\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_dy_ord_inc.csv\", \"condition_exclude_query\":\"/tmp/job_db_to_file/JN_CP001-DF01_001/JN_CP001-DF01_001_temp_csv_dy_ord_exc.csv\"}",
                                "sub_parent_column": "campaign_instructions_code,campaign_group_no",
                                "sync_update_skip_flg": "1"
                              }
                            },
                            "End": true
                          }
                        }
                      },
                      "End": true,
                      "Items": "{% $dynamic_parallel_num_array %}",
                      "ItemSelector": {
                        "split_num": "{% $states.context.Map.Item.Value %}"
                      }
                    }
                  }
                }
              ],
              "Next": "parallel_compress_file"
            },
            "parallel_compress_file": {
              "QueryLanguage": "JSONata",
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "ZIP_Map_Promotions",
                  "States": {
                    "ZIP_Map_Promotions": {
                      "QueryLanguage": "JSONata",
                      "Type": "Map",
                      "ItemProcessor": {
                        "ProcessorConfig": {
                          "Mode": "INLINE"
                        },
                        "StartAt": "27_job_file_compress",
                        "States": {
                          "27_job_file_compress": {
                            "QueryLanguage": "JSONata",
                            "Type": "Task",
                            "Resource": "arn:aws:states:::glue:startJobRun.sync",
                            "Arguments": {
                              "JobName": "job_file_compress",
                              "Arguments": {
                                "input_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "file_name": "{% 'txCampaignPromotions_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                                "output_file_dir": "{% 'input-output/EC_OUT/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "output_file_name": "{% 'txCampaignPromotions_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.zip' %}",
                                "backup_flag": "True",
                                "backup_file_dir": "{% 'back-up/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "jobnet_id": "JN_CP001-DF01_001"
                              }
                            },
                            "End": true
                          }
                        }
                      },
                      "Items": "{% $promotions_parallel_num_array %}",
                      "ItemSelector": {
                        "split_num": "{% $states.context.Map.Item.Value %}"
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "ZIP_Map_Pricebooks",
                  "States": {
                    "ZIP_Map_Pricebooks": {
                      "QueryLanguage": "JSONata",
                      "Type": "Map",
                      "ItemProcessor": {
                        "ProcessorConfig": {
                          "Mode": "INLINE"
                        },
                        "StartAt": "28_job_file_compress",
                        "States": {
                          "28_job_file_compress": {
                            "QueryLanguage": "JSONata",
                            "Type": "Task",
                            "Resource": "arn:aws:states:::glue:startJobRun.sync",
                            "Arguments": {
                              "JobName": "job_file_compress",
                              "Arguments": {
                                "input_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "file_name": "{% 'txCampaignPricebooks_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                                "output_file_dir": "{% 'input-output/EC_OUT/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "output_file_name": "{% 'txCampaignPricebooks_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.zip' %}",
                                "backup_flag": "True",
                                "backup_file_dir": "{% 'back-up/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "jobnet_id": "JN_CP001-DF01_001"
                              }
                            },
                            "End": true
                          }
                        }
                      },
                      "Items": "{% $pricebooks_parallel_num_array %}",
                      "ItemSelector": {
                        "split_num": "{% $states.context.Map.Item.Value %}"
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "ZIP_Map_Static",
                  "States": {
                    "ZIP_Map_Static": {
                      "QueryLanguage": "JSONata",
                      "Type": "Map",
                      "ItemProcessor": {
                        "ProcessorConfig": {
                          "Mode": "INLINE"
                        },
                        "StartAt": "29_job_file_compress",
                        "States": {
                          "29_job_file_compress": {
                            "QueryLanguage": "JSONata",
                            "Type": "Task",
                            "Resource": "arn:aws:states:::glue:startJobRun.sync",
                            "Arguments": {
                              "JobName": "job_file_compress",
                              "Arguments": {
                                "input_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "file_name": "{% 'txStaticCustomerGroups_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                                "output_file_dir": "{% 'input-output/EC_OUT/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "output_file_name": "{% 'txStaticCustomerGroups_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.zip' %}",
                                "backup_flag": "True",
                                "backup_file_dir": "{% 'back-up/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "jobnet_id": "JN_CP001-DF01_001"
                              }
                            },
                            "End": true
                          }
                        }
                      },
                      "Items": "{% $static_parallel_num_array %}",
                      "ItemSelector": {
                        "split_num": "{% $states.context.Map.Item.Value %}"
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "ZIP_Map_Dynamic",
                  "States": {
                    "ZIP_Map_Dynamic": {
                      "QueryLanguage": "JSONata",
                      "Type": "Map",
                      "ItemProcessor": {
                        "ProcessorConfig": {
                          "Mode": "INLINE"
                        },
                        "StartAt": "30_job_file_compress",
                        "States": {
                          "30_job_file_compress": {
                            "QueryLanguage": "JSONata",
                            "Type": "Task",
                            "Resource": "arn:aws:states:::glue:startJobRun.sync",
                            "Arguments": {
                              "JobName": "job_file_compress",
                              "Arguments": {
                                "input_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "file_name": "{% 'txDynamicCustomerGroups_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                                "output_file_dir": "{% 'input-output/EC_OUT/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "output_file_name": "{% 'txDynamicCustomerGroups_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.zip' %}",
                                "backup_flag": "True",
                                "backup_file_dir": "{% 'back-up/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                                "jobnet_id": "JN_CP001-DF01_001"
                              }
                            },
                            "End": true
                          }
                        }
                      },
                      "Items": "{% $dynamic_parallel_num_array %}",
                      "ItemSelector": {
                        "split_num": "{% $states.context.Map.Item.Value %}"
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "31_job_execute_sql"
            },
            "31_job_execute_sql": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_CP001-DF01_001",
                  "sql_info": "{\"sql_info\": [{\"query_id\": \"sql_CP001-DF01_after_001\"}]}"
                }
              },
              "End": true
            }
          }
        }
