CREATE TABLE wk_pr003_df01_set_commodity_composition_list (
    product_id VARCHAR(16) NOT NULL,
    child_commodity_code VARCHAR(16) NOT NULL,
    oms_delete_flg NUMERIC(1) DEFAULT 0,
    composition_quantity NUMERIC(2, 0),
    tax_exc INTEGER,
    PRIMARY KEY (product_id, child_commodity_code, oms_delete_flg)
);
COMMENT ON TABLE wk_pr003_df01_set_commodity_composition_list IS '商品連携_セット商品ワークテーブル';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_composition_list.product_id IS '通販商品番号';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_composition_list.child_commodity_code IS '子商品コード';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_composition_list.oms_delete_flg IS 'OMS論理削除フラグ';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_composition_list.composition_quantity IS '数量';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_composition_list.tax_exc IS '税込み金額';