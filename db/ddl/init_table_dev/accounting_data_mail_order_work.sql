CREATE TABLE accounting_data_mail_order_work (
    input_no VARCHAR(8) NOT NULL,
    input_sys_type VARCHAR(3) NOT NULL,
    corp_cd VARCHAR(12) NOT NULL,
    vote_employee_cd VARCHAR(12) NOT NULL,
    vote_dept_cd VARCHAR(12) NOT NULL,
    approval_employee_cd VARCHAR(12) NOT NULL,
    approval_date TIMESTAMP NOT NULL,
    approval_status_type VARCHAR(1) NOT NULL,
    journal_type VARCHAR(2) NOT NULL,
    slip_date TIMESTAMP NOT NULL,
    slip_no VARCHAR(8),
    slip_ope_ban_type VARCHAR(1) NOT NULL,
    journal_reference_type VARCHAR(1) NOT NULL,
    input_unit_no VARCHAR(20),
    xml_db_seq_key VARCHAR(4),
    transfer_link_key VARCHAR(12),
    sys_reserve1 VARCHAR(4),
    fixes_reason_code VARCHAR(12),
    business_code VA<PERSON><PERSON>R(3),
    form_code VA<PERSON><PERSON><PERSON>(12),
    order_top_char VARCHAR(2),
    order_item VARCHAR(4),
    order_other VARCHAR(18),
    wh_code VA<PERSON><PERSON><PERSON>(8),
    join_segment_code_1 VARCHAR(12),
    join_segment_code_2 VARCHAR(12),
    join_segment_code_3 VARCHAR(12),
    counter_corp_cd VARCHAR(12),
    counter_corp_join_segment_code_1 VARCHAR(12),
    counter_corp_join_segment_code_2 VARCHAR(12),
    counter_corp_join_segment_code_3 VARCHAR(12),
    slip_user_open_date_1 TIMESTAMP,
    slip_user_open_code_1 VARCHAR(24),
    slip_user_open_code_2 VARCHAR(16),
    sys_reserve2 VARCHAR(12),
    slip_remarks VARCHAR(192) NOT NULL,
    approval_remarks VARCHAR(192),
    slip_user_open_area VARCHAR(192),
    slip_user_open_area_2 VARCHAR(60),
    line_num VARCHAR(9) NOT NULL,
    slip_detail_lending_type VARCHAR(1) NOT NULL,
    account_code VARCHAR(12) NOT NULL,
    accounting_dept_code VARCHAR(12) NOT NULL,
    details_type VARCHAR(4),
    details_code VARCHAR(12) NOT NULL,
    items_type VARCHAR(4),
    items_code VARCHAR(12),
    count_ext_code_1 VARCHAR(4),
    count_ext_code_1_type VARCHAR(12),
    count_ext_code_2 VARCHAR(4),
    count_ext_code_2_type VARCHAR(12),
    count_ext_code_3 VARCHAR(4),
    count_ext_code_3_type VARCHAR(12),
    count_ext_code_4 VARCHAR(4),
    count_ext_code_4_type VARCHAR(12),
    count_ext_code_5 VARCHAR(4),
    count_ext_code_5_type VARCHAR(12),
    search_ext_code_1 VARCHAR(4),
    search_ext_code_1_type VARCHAR(12),
    search_ext_code_2 VARCHAR(4),
    search_ext_code_2_type VARCHAR(12),
    search_ext_code_3 VARCHAR(4),
    search_ext_code_3_type VARCHAR(12),
    search_ext_code_4 VARCHAR(4),
    search_ext_code_4_type VARCHAR(12),
    search_ext_code_5 VARCHAR(4),
    search_ext_code_5_type VARCHAR(12),
    business_partner_code VARCHAR(12),
    segment_code VARCHAR(12) NOT NULL,
    cost_burden_center_code VARCHAR(12),
    bill_cash_code VARCHAR(12),
    business_segment_code VARCHAR(12),
    region_segment_code VARCHAR(12),
    customer_segment_code VARCHAR(12),
    user_open_segment_code_1 VARCHAR(12) NOT NULL,
    user_open_segment_code_2 VARCHAR(12),
    match_key VARCHAR(12),
    tran_currency_code VARCHAR(4),
    tran_currency_exchange_rate_type VARCHAR(1),
    tran_currency_rate VARCHAR(13),
    view_currency_exchange_rate_type_1 VARCHAR(1),
    view_currency_rate_1 VARCHAR(13),
    view_currency_exchange_rate_type_2 VARCHAR(1),
    view_currency_rate_2 VARCHAR(13),
    view_currency_exchange_rate_type_3 VARCHAR(1),
    view_currency_rate_3 VARCHAR(13),
    funding_code VARCHAR(12),
    tax_type_code VARCHAR(4),
    sys_reserve3 VARCHAR(4),
    tax_rate_type VARCHAR(6),
    function_currency_amout NUMERIC(20) NOT NULL,
    tran_currency_amout NUMERIC(20),
    reference_tax NUMERIC(20),
    user_open_num_1 VARCHAR(20),
    tax_type VARCHAR(1) NOT NULL,
    history_property_code VARCHAR(36),
    counter_account_code VARCHAR(12),
    sys_reserve4 VARCHAR(12),
    sys_reserve5 VARCHAR(4),
    sys_reserve6 VARCHAR(12),
    sys_reserve7 VARCHAR(4),
    sys_reserve8 VARCHAR(12),
    sys_reserve9 VARCHAR(4),
    sys_reserve10 VARCHAR(12),
    sys_reserve11 VARCHAR(4),
    sys_reserve12 VARCHAR(12),
    sys_reserve13 VARCHAR(4),
    sys_reserve14 VARCHAR(12),
    sys_reserve15 VARCHAR(4),
    sys_reserve16 VARCHAR(12),
    sys_reserve17 VARCHAR(4),
    sys_reserve18 VARCHAR(12),
    sys_reserve19 VARCHAR(12),
    sys_reserve20 VARCHAR(12),
    sys_reserve21 VARCHAR(12),
    sys_reserve22 VARCHAR(12),
    sys_reserve23 VARCHAR(4),
    sys_reserve24 VARCHAR(6),
    sys_reserve25 VARCHAR(4),
    sys_reserve26 VARCHAR(12),
    sys_reserve27 VARCHAR(12),
    sys_reserve28 VARCHAR(12),
    quantity NUMERIC(20),
    unit_cd VARCHAR(12),
    quantity_sub NUMERIC(20),
    unit_cd_sub VARCHAR(12),
    function_currency_price NUMERIC(20),
    tran_currency_price NUMERIC(20),
    ext_num_1 VARCHAR(20),
    ext_num_2 VARCHAR(20),
    ext_num_3 VARCHAR(20),
    user_open_date_1 TIMESTAMP,
    user_open_code_1 VARCHAR(12),
    user_open_code_2 VARCHAR(12),
    user_open_code_3 VARCHAR(4),
    user_open_code_4 VARCHAR(24),
    user_open_code_5 VARCHAR(24),
    user_open_code_6 VARCHAR(24),
    user_open_code_7 VARCHAR(24),
    user_open_area_1 VARCHAR(72),
    sys_reserve29 VARCHAR(24),
    sys_reserve30 VARCHAR(72),
    user_open_area_2 VARCHAR(36),
    user_open_area_3 VARCHAR(36),
    user_open_code_8 VARCHAR(8),
    user_open_area_5 VARCHAR(24),
    user_open_area_6 VARCHAR(36),
    user_open_area_7 VARCHAR(60),
    user_open_area_8 VARCHAR(72),
    sys_reserve31 VARCHAR(2),
    user_open_date_2 TIMESTAMP,
    text_description_bill_remarks VARCHAR(192) NOT NULL,
    detail_user_open_area VARCHAR(192),
    detail_user_open_area_2 VARCHAR(384),
    individual_application_key VARCHAR(12),
    recovery_payment_dept_code VARCHAR(12),
    contract_no VARCHAR(12),
    invoice_no VARCHAR(12),
    recovery_payment_schedule_date TIMESTAMP,
    bill_cash_closing_date TIMESTAMP,
    upd_sub_sys_type VARCHAR(1),
    property_control_number VARCHAR(36),
    bill_no VARCHAR(36),
    bill_kind_type VARCHAR(1),
    bill_type VARCHAR(1),
    transition_type VARCHAR(2),
    bill_cash_settlement_date TIMESTAMP,
    bill_split_type_sys_reserve VARCHAR(1),
    effort_payment_advice_date TIMESTAMP,
    cash_schedule_date TIMESTAMP,
    bill_site VARCHAR(4),
    sys_reserve32 VARCHAR(4),
    sys_reserve33 VARCHAR(15),
    bank_account_holder VARCHAR(192),
    payment_place_counter_bank_code VARCHAR(12),
    payment_place VARCHAR(96),
    bill_effort_company_bank_code VARCHAR(12),
    bill_discount_fee VARCHAR(13),
    telegraph_document_transfer_type VARCHAR(1),
    fee_burden_type VARCHAR(1),
    fb_transfer_process_type VARCHAR(1),
    company_bank_account_type VARCHAR(1),
    company_bank_account_no VARCHAR(12),
    counter_bank_account_type VARCHAR(1),
    counter_bank_account_no VARCHAR(12),
    sys_reserve34 VARCHAR(22),
    sys_reserve35 VARCHAR(11)
);
COMMENT ON TABLE accounting_data_mail_order_work IS '仕訳データ（通販）ワーク';
COMMENT ON COLUMN accounting_data_mail_order_work.input_no IS '入力番号';
COMMENT ON COLUMN accounting_data_mail_order_work.input_sys_type IS '入力システム区分';
COMMENT ON COLUMN accounting_data_mail_order_work.corp_cd IS '会社コード';
COMMENT ON COLUMN accounting_data_mail_order_work.vote_employee_cd IS '起票社員コード';
COMMENT ON COLUMN accounting_data_mail_order_work.vote_dept_cd IS '起票部門コード';
COMMENT ON COLUMN accounting_data_mail_order_work.approval_employee_cd IS '承認社員コード';
COMMENT ON COLUMN accounting_data_mail_order_work.approval_date IS '承認日付';
COMMENT ON COLUMN accounting_data_mail_order_work.approval_status_type IS '承認状態区分';
COMMENT ON COLUMN accounting_data_mail_order_work.journal_type IS '仕訳種別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.slip_date IS '伝票日付';
COMMENT ON COLUMN accounting_data_mail_order_work.slip_no IS '伝票番号';
COMMENT ON COLUMN accounting_data_mail_order_work.slip_ope_ban_type IS '伝票操作禁止区分';
COMMENT ON COLUMN accounting_data_mail_order_work.journal_reference_type IS '仕訳基準種別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.input_unit_no IS '入力単位番号';
COMMENT ON COLUMN accounting_data_mail_order_work.xml_db_seq_key IS 'XML-DB連番キー';
COMMENT ON COLUMN accounting_data_mail_order_work.transfer_link_key IS '振替リンクキー';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve1 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.fixes_reason_code IS '修正事由コード';
COMMENT ON COLUMN accounting_data_mail_order_work.business_code IS '業務コード';
COMMENT ON COLUMN accounting_data_mail_order_work.form_code IS '形態コード';
COMMENT ON COLUMN accounting_data_mail_order_work.order_top_char IS 'オーダ 冠記号';
COMMENT ON COLUMN accounting_data_mail_order_work.order_item IS 'オーダ 費目';
COMMENT ON COLUMN accounting_data_mail_order_work.order_other IS 'オーダ その他';
COMMENT ON COLUMN accounting_data_mail_order_work.wh_code IS '倉庫コード';
COMMENT ON COLUMN accounting_data_mail_order_work.join_segment_code_1 IS '連結セグメントコード1';
COMMENT ON COLUMN accounting_data_mail_order_work.join_segment_code_2 IS '連結セグメントコード2';
COMMENT ON COLUMN accounting_data_mail_order_work.join_segment_code_3 IS '連結セグメントコード3';
COMMENT ON COLUMN accounting_data_mail_order_work.counter_corp_cd IS '相手会社コード';
COMMENT ON COLUMN accounting_data_mail_order_work.counter_corp_join_segment_code_1 IS '相手会社連結セグメントコード1';
COMMENT ON COLUMN accounting_data_mail_order_work.counter_corp_join_segment_code_2 IS '相手会社連結セグメントコード2';
COMMENT ON COLUMN accounting_data_mail_order_work.counter_corp_join_segment_code_3 IS '相手会社連結セグメントコード3';
COMMENT ON COLUMN accounting_data_mail_order_work.slip_user_open_date_1 IS '伝票ユーザ開放日付1
(遡及年月日)';
COMMENT ON COLUMN accounting_data_mail_order_work.slip_user_open_code_1 IS '伝票ユーザ開放コード1
(注文番号/社給番号/伝票番号)';
COMMENT ON COLUMN accounting_data_mail_order_work.slip_user_open_code_2 IS '伝票ユーザ開放コード2
(請求支払日付・回収予定日付)';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve2 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.slip_remarks IS '伝票備考';
COMMENT ON COLUMN accounting_data_mail_order_work.approval_remarks IS '承認備考';
COMMENT ON COLUMN accounting_data_mail_order_work.slip_user_open_area IS '伝票ユーザ開放域';
COMMENT ON COLUMN accounting_data_mail_order_work.slip_user_open_area_2 IS '伝票ユーザ開放域2
(付加情報3)';
COMMENT ON COLUMN accounting_data_mail_order_work.line_num IS '行番号';
COMMENT ON COLUMN accounting_data_mail_order_work.slip_detail_lending_type IS '伝票明細貸借区分';
COMMENT ON COLUMN accounting_data_mail_order_work.account_code IS '勘定科目コード';
COMMENT ON COLUMN accounting_data_mail_order_work.accounting_dept_code IS '会計部門コード';
COMMENT ON COLUMN accounting_data_mail_order_work.details_type IS '細目識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.details_code IS '細目コード';
COMMENT ON COLUMN accounting_data_mail_order_work.items_type IS '内訳識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.items_code IS '内訳コード';
COMMENT ON COLUMN accounting_data_mail_order_work.count_ext_code_1 IS '集計拡張コード1識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.count_ext_code_1_type IS '集計拡張コード1';
COMMENT ON COLUMN accounting_data_mail_order_work.count_ext_code_2 IS '集計拡張コード2識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.count_ext_code_2_type IS '集計拡張コード2';
COMMENT ON COLUMN accounting_data_mail_order_work.count_ext_code_3 IS '集計拡張コード3識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.count_ext_code_3_type IS '集計拡張コード3';
COMMENT ON COLUMN accounting_data_mail_order_work.count_ext_code_4 IS '集計拡張コード4識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.count_ext_code_4_type IS '集計拡張コード4';
COMMENT ON COLUMN accounting_data_mail_order_work.count_ext_code_5 IS '集計拡張コード5識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.count_ext_code_5_type IS '集計拡張コード5';
COMMENT ON COLUMN accounting_data_mail_order_work.search_ext_code_1 IS '検索拡張コード1識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.search_ext_code_1_type IS '検索拡張コード1';
COMMENT ON COLUMN accounting_data_mail_order_work.search_ext_code_2 IS '検索拡張コード2識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.search_ext_code_2_type IS '検索拡張コード2';
COMMENT ON COLUMN accounting_data_mail_order_work.search_ext_code_3 IS '検索拡張コード3識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.search_ext_code_3_type IS '検索拡張コード3';
COMMENT ON COLUMN accounting_data_mail_order_work.search_ext_code_4 IS '検索拡張コード4識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.search_ext_code_4_type IS '検索拡張コード4';
COMMENT ON COLUMN accounting_data_mail_order_work.search_ext_code_5 IS '検索拡張コード5識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.search_ext_code_5_type IS '検索拡張コード5';
COMMENT ON COLUMN accounting_data_mail_order_work.business_partner_code IS '取引先コード';
COMMENT ON COLUMN accounting_data_mail_order_work.segment_code IS 'セグメントコード';
COMMENT ON COLUMN accounting_data_mail_order_work.cost_burden_center_code IS '負担元コストセンタコード';
COMMENT ON COLUMN accounting_data_mail_order_work.bill_cash_code IS '請求支払先コード';
COMMENT ON COLUMN accounting_data_mail_order_work.business_segment_code IS '事業セグメントコード';
COMMENT ON COLUMN accounting_data_mail_order_work.region_segment_code IS '地域セグメントコード';
COMMENT ON COLUMN accounting_data_mail_order_work.customer_segment_code IS '顧客セグメントコード';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_segment_code_1 IS 'ユーザ開放セグメントコード1';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_segment_code_2 IS 'ユーザ開放セグメントコード2';
COMMENT ON COLUMN accounting_data_mail_order_work.match_key IS 'マッチングキー';
COMMENT ON COLUMN accounting_data_mail_order_work.tran_currency_code IS '取引通貨コード';
COMMENT ON COLUMN accounting_data_mail_order_work.tran_currency_exchange_rate_type IS '取引通貨為替レート識別区分';
COMMENT ON COLUMN accounting_data_mail_order_work.tran_currency_rate IS '取引通貨レート';
COMMENT ON COLUMN accounting_data_mail_order_work.view_currency_exchange_rate_type_1 IS '表示通貨為替レート識別区分1';
COMMENT ON COLUMN accounting_data_mail_order_work.view_currency_rate_1 IS '表示通貨レート1';
COMMENT ON COLUMN accounting_data_mail_order_work.view_currency_exchange_rate_type_2 IS '表示通貨為替レート識別区分2';
COMMENT ON COLUMN accounting_data_mail_order_work.view_currency_rate_2 IS '表示通貨レート2';
COMMENT ON COLUMN accounting_data_mail_order_work.view_currency_exchange_rate_type_3 IS '表示通貨為替レート識別区分3';
COMMENT ON COLUMN accounting_data_mail_order_work.view_currency_rate_3 IS '表示通貨レート3';
COMMENT ON COLUMN accounting_data_mail_order_work.funding_code IS '資金コード';
COMMENT ON COLUMN accounting_data_mail_order_work.tax_type_code IS '消費税区分コード';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve3 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.tax_rate_type IS '税率区分';
COMMENT ON COLUMN accounting_data_mail_order_work.function_currency_amout IS '機能通貨発生金額';
COMMENT ON COLUMN accounting_data_mail_order_work.tran_currency_amout IS '取引通貨発生金額';
COMMENT ON COLUMN accounting_data_mail_order_work.reference_tax IS '参考消費税金額';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_num_1 IS 'ユーザ開放数値1';
COMMENT ON COLUMN accounting_data_mail_order_work.tax_type IS '課税区分';
COMMENT ON COLUMN accounting_data_mail_order_work.history_property_code IS '履歴物件コード';
COMMENT ON COLUMN accounting_data_mail_order_work.counter_account_code IS '相手勘定科目コード';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve4 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve5 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve6 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve7 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve8 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve9 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve10 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve11 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve12 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve13 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve14 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve15 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve16 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve17 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve18 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve19 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve20 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve21 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve22 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve23 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve24 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve25 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve26 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve27 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve28 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.quantity IS '数量';
COMMENT ON COLUMN accounting_data_mail_order_work.unit_cd IS '単位コード';
COMMENT ON COLUMN accounting_data_mail_order_work.quantity_sub IS '数量(副)';
COMMENT ON COLUMN accounting_data_mail_order_work.unit_cd_sub IS '単位コード(副)';
COMMENT ON COLUMN accounting_data_mail_order_work.function_currency_price IS '機能通貨単価';
COMMENT ON COLUMN accounting_data_mail_order_work.tran_currency_price IS '取引通貨単価';
COMMENT ON COLUMN accounting_data_mail_order_work.ext_num_1 IS '拡張数値1';
COMMENT ON COLUMN accounting_data_mail_order_work.ext_num_2 IS '拡張数値2';
COMMENT ON COLUMN accounting_data_mail_order_work.ext_num_3 IS '拡張数値3';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_date_1 IS 'ユーザ開放日付1
(主オーダ発行日)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_code_1 IS 'ユーザ開放コード1
(債権債務発生社員コード)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_code_2 IS 'ユーザ開放コード2
(仕損コード)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_code_3 IS 'ユーザ開放コード3
(支払区分コード)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_code_4 IS 'ユーザ開放コード4
(オーダ明細番号)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_code_5 IS 'ユーザ開放コード5
(代表機種コード)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_code_6 IS 'ユーザ開放コード6
(払出元コード)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_code_7 IS 'ユーザ開放コード7
(発生元品名コード)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_area_1 IS 'ユーザ開放域1
(発生元品名(漢字))';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve29 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve30 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_area_2 IS 'ユーザ開放域2
(付加情報1)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_area_3 IS 'ユーザ開放域3
(付加情報2)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_code_8 IS 'ユーザ開放コード8
(取引時間)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_area_5 IS 'ユーザ開放域5
(タイムゾーン)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_area_6 IS 'ユーザ開放域6
(資金収支プロジェクトコード)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_area_7 IS 'ユーザ開放域7
(空き1)';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_area_8 IS 'ユーザ開放域8
(空き2)';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve31 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.user_open_date_2 IS 'ユーザ開放日付2
(決算年月)';
COMMENT ON COLUMN accounting_data_mail_order_work.text_description_bill_remarks IS '文字摘要／手形備考';
COMMENT ON COLUMN accounting_data_mail_order_work.detail_user_open_area IS '明細ユーザ開放域';
COMMENT ON COLUMN accounting_data_mail_order_work.detail_user_open_area_2 IS '明細ユーザ開放域2';
COMMENT ON COLUMN accounting_data_mail_order_work.individual_application_key IS '個別消込キー';
COMMENT ON COLUMN accounting_data_mail_order_work.recovery_payment_dept_code IS '回収支払部門コード';
COMMENT ON COLUMN accounting_data_mail_order_work.contract_no IS '契約番号';
COMMENT ON COLUMN accounting_data_mail_order_work.invoice_no IS 'インボイス番号';
COMMENT ON COLUMN accounting_data_mail_order_work.recovery_payment_schedule_date IS '回収支払予定日付';
COMMENT ON COLUMN accounting_data_mail_order_work.bill_cash_closing_date IS '請求支払締め日付';
COMMENT ON COLUMN accounting_data_mail_order_work.upd_sub_sys_type IS '更新サブシステム区分';
COMMENT ON COLUMN accounting_data_mail_order_work.property_control_number IS '物件管理番号';
COMMENT ON COLUMN accounting_data_mail_order_work.bill_no IS '手形番号';
COMMENT ON COLUMN accounting_data_mail_order_work.bill_kind_type IS '手形種類区分';
COMMENT ON COLUMN accounting_data_mail_order_work.bill_type IS '手形区分';
COMMENT ON COLUMN accounting_data_mail_order_work.transition_type IS '推移区分';
COMMENT ON COLUMN accounting_data_mail_order_work.bill_cash_settlement_date IS '手形期日/期日現金決済日';
COMMENT ON COLUMN accounting_data_mail_order_work.bill_split_type_sys_reserve IS '手形分割区分
(システムリザーブ)';
COMMENT ON COLUMN accounting_data_mail_order_work.effort_payment_advice_date IS '取組日付/支払通知日付';
COMMENT ON COLUMN accounting_data_mail_order_work.cash_schedule_date IS '現金化予定日付';
COMMENT ON COLUMN accounting_data_mail_order_work.bill_site IS '手形サイト';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve32 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve33 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.bank_account_holder IS '振出人名称
/自社銀行口座名義人
/相手銀行口座名義人';
COMMENT ON COLUMN accounting_data_mail_order_work.payment_place_counter_bank_code IS '支払場所銀行コード
/相手銀行コード';
COMMENT ON COLUMN accounting_data_mail_order_work.payment_place IS '支払場所';
COMMENT ON COLUMN accounting_data_mail_order_work.bill_effort_company_bank_code IS '手形取組銀行コード
/自社銀行コード';
COMMENT ON COLUMN accounting_data_mail_order_work.bill_discount_fee IS '手形割引手数料';
COMMENT ON COLUMN accounting_data_mail_order_work.telegraph_document_transfer_type IS '電信文書振込区分';
COMMENT ON COLUMN accounting_data_mail_order_work.fee_burden_type IS '手数料負担区分';
COMMENT ON COLUMN accounting_data_mail_order_work.fb_transfer_process_type IS 'FB振込処理区分';
COMMENT ON COLUMN accounting_data_mail_order_work.company_bank_account_type IS '自社銀行口座種別';
COMMENT ON COLUMN accounting_data_mail_order_work.company_bank_account_no IS '自社銀行口座番号';
COMMENT ON COLUMN accounting_data_mail_order_work.counter_bank_account_type IS '相手銀行口座種別';
COMMENT ON COLUMN accounting_data_mail_order_work.counter_bank_account_no IS '相手銀行口座番号';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve34 IS 'システムリザーブ';
COMMENT ON COLUMN accounting_data_mail_order_work.sys_reserve35 IS 'システムリザーブ';

