#!/usr/bin/env python3
"""
正しいCSVデータの検証
"""

import csv
import io

def verify_correct_csv():
    """正しいCSVデータの検証"""
    
    print("=== 正しいCSV→TSV変換の検証 ===\n")
    
    # 正しいCSVデータ（RFC4180準拠）
    csv_data = '"ID","商品名","価格","備考"\r\n"1","商品A,特別版","100","通常商品"\r\n"2","商品B""限定""","200","限定商品"'
    
    print("【入力CSVデータ】")
    print(f"Raw: {repr(csv_data)}")
    print(f"表示: \n{csv_data}")
    
    print("\n【CSV解析結果】")
    csv_reader = csv.reader(io.StringIO(csv_data), delimiter=',', quotechar='"')
    rows = list(csv_reader)
    for i, row in enumerate(rows):
        print(f"Row {i}: {row}")
    
    print("\n【TSV出力（囲み文字なし）】")
    tsv_output = io.StringIO()
    tsv_writer = csv.writer(tsv_output, delimiter='\t', quotechar='', quoting=csv.QUOTE_NONE, lineterminator='\r\n')
    for row in rows:
        tsv_writer.writerow(row)
    tsv_result = tsv_output.getvalue()
    print(f"Raw: {repr(tsv_result)}")
    print(f"表示: \n{tsv_result}")
    
    print("\n【SJIS エンコード/デコード テスト】")
    try:
        # SJIS エンコード
        csv_sjis = csv_data.encode('shift_jis')
        print(f"SJIS エンコード成功: {len(csv_sjis)} bytes")
        
        # SJIS デコード
        csv_decoded = csv_sjis.decode('shift_jis')
        print(f"SJIS デコード成功: {csv_data == csv_decoded}")
        
        # TSV SJIS エンコード
        tsv_sjis = tsv_result.encode('shift_jis')
        print(f"TSV SJIS エンコード成功: {len(tsv_sjis)} bytes")
        
    except Exception as e:
        print(f"エンコード/デコード エラー: {e}")
    
    print("\n【特殊文字の処理確認】")
    print("1. カンマを含むデータ: '商品A,特別版' → 正しく1つのフィールドとして解析")
    print("2. ダブルクォーテーションを含むデータ: '商品B\"限定\"' → エスケープ処理が正常")
    print("3. 全フィールドが囲み文字で囲まれている → RFC4180準拠")

if __name__ == "__main__":
    verify_correct_csv()
