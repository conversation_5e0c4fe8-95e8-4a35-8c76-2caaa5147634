AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_CV003-FF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF-CV003-FF01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "1_job_send_file"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "1_job_send_file": {
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Parameters": {
                "JobName": "job_send_file",
                "Arguments": {
                  "s3_full_path": "ext-input-output/DLPF_WMS/JN_CV003-FF01_001/TSagawaEDI_*",
                  "secret_name": "SAGAWA_SFTP_INFO",
                  "external_system_destination_directory": "./",
                  "external_system_transmission_file_name": "R0400000000.sftp",
                  "backup_flag": "True",
                  "backup_s3_directory": "ext-back-up/DLPF_WMS/JN_CV003-FF01_001/",
                  "jobnet_id": "JN_CV003-FF01_001",
                  "external_system_rename_file_name": "R0400000000.txt"
                }
              },
              "End": true
            }
          }
        }
