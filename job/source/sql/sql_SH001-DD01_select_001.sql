SELECT 
    order_no,
    shop_code,
    order_datetime,
    customer_code,
    neo_customer_no,
    guest_flg,
    last_name,
    first_name,
    last_name_kana,
    first_name_kana,
    email,
    birth_date,
    sex,
    postal_code,
    prefecture_code,
    address1,
    address2,
    address3,
    address4,
    corporation_post_name,
    phone_number,
    advance_later_flg,
    payment_method_no,
    payment_method_type,
    payment_method_name,
    ext_payment_method_type,
    payment_commission,
    payment_commission_tax_gr_code,
    payment_commission_tax_no,
    payment_commission_tax_rate,
    payment_commission_tax,
    payment_commission_tax_type,
    coupon_management_code,
    coupon_code,
    coupon_name,
    coupon_type,
    coupon_use_purchase_price,
    coupon_discount_type,
    coupon_discount_price,
    coupon_discount_rate,
    coupon_used_amount,
    coupon_start_datetime,
    coupon_end_datetime,
    coupon_kbn,
    goods_group,
    commodity_category_code,
    commodity_series,
    coupon_commodity_code_display,
    baitai_name,
    used_point,
    total_amount,
    ec_promotion_id,
    ec_promotion_name,
    ec_promotion_discount_price,
    ec_campaign_id,
    ec_campaign_name,
    payment_date,
    payment_limit_date,
    payment_status,
    ext_payment_status,
    customer_group_code,
    data_transport_status,
    order_status,
    ext_order_status,
    tax_reference_date,
    cancel_date,
    client_group,
    device_type,
    caution,
    message,
    payment_order_id,
    cvs_code,
    payment_receipt_no,
    payment_receipt_url,
    receipt_no,
    customer_no,
    confirm_no,
    career_key,
    order_create_error_code,
    order_display_status,
    order_kind_kbn,
    marketing_channel,
    original_order_no,
    external_order_no,
    order_recieve_datetime,
    order_update_datetime,
    order_update_reason_kbn,
    cancel_reason_kbn,
    uncollectible_date,
    order_total_price,
    account_receivable_balance,
    appropriate_amount,
    bill_address_kbn,
    receipt_flg,
    receipt_to,
    receipt_detail,
    bill_price,
    bill_no,
    bill_print_count,
    authority_result_kbn,
    authority_no,
    card_password,
    authority_approval_no,
    authority_date,
    authority_price,
    authority_cancel_approval_no,
    authority_cancel_date,
    credit_payment_no,
    credit_payment_date,
    credit_payment_price,
    credit_cancel_payment_no,
    credit_cancel_payment_date,
    credit_result_kbn,
    card_brand,
    credit_card_kanri_no,
    credit_card_kanri_detail_no,
    credit_card_no,
    credit_card_meigi,
    credit_card_valid_year,
    credit_card_valid_month,
    credit_card_pay_count,
    payment_bar_code,
    amzn_charge_permission_id,
    amzn_charge_id,
    amzn_charge_status,
    amzn_authorization_datetime,
    amzn_capture_initiated_datetime,
    amzn_captured_datetime,
    amzn_canceled_datetime,
    order_user_code,
    order_user,
    change_user_code,
    change_user,
    demand_kbn,
    demand1_ref_date,
    demand1_date,
    demand1_limit_date,
    demand1_amount,
    demand1_bar_code,
    demand2_ref_date,
    demand2_date,
    demand2_limit_date,
    demand2_amount,
    demand2_bar_code,
    demand3_ref_date,
    demand3_date,
    demand3_limit_date,
    demand3_amount,
    demand3_bar_code,
    kashidaore_date,
    demand_exclude_reason_kbn,
    demand_exclude_start_date,
    demand_exclude_end_date,
    bill_sei_kj,
    bill_mei_kj,
    bill_sei_kn,
    bill_mei_kn,
    bill_tel_no,
    bill_zipcd,
    bill_addr1,
    bill_addr2,
    bill_addr3,
    bill_addr4,
    bill_corporation_post_name,
    nohinsyo_uketsuke_tanto,
    grant_plan_point_prod,
    grant_plan_point_other,
    grant_plan_point_total,
    grant_point_prod,
    grant_point_other,
    grant_point_total,
    reduction_plan_point_total,
    reduction_point_total,
    subtotal_before_campaign,
    subtotal_after_campaign,
    total_before_campaign,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM 
    order_header
where
    updated_datetime > :sync_datetime
AND updated_datetime <= :diff_base_timestamp;
