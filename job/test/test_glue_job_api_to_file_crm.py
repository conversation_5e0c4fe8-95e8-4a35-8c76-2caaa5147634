#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import traceback
import pandas as pd
import pprint

# from unittest import mock
import boto3
from typing import Any
import pytest
from unittest.mock import patch
from source.glue_job_api_to_file_crm import GlueJobApiToFileCrm, get_params, main


import json
from moto import mock_secretsmanager, mock_ssm
import responses


@pytest.fixture
def aws_credentials():
    """Mocked AWS Credentials for moto"""
    # os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    # os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    # os.environ["AWS_SECURITY_TOKEN"] = "testing"
    # os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "ap-northeast-1"


@pytest.fixture
def mock_secrets():
    """Secrets Managerのmock - 実際のDB接続情報を使用"""

    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際のDB接続情報を設定
        db_secret = {
            # "username": "dlpf_batch",
            "username": "dlpf_ope",
            "password": "password",
            "host": "************",
            "port": "5432",
            "dbname": "test_db",
        }
        secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))
        yield secrets


@pytest.fixture
def mock_secrets_error():
    """Secrets Managerのmock - 実際のDB接続情報を使用"""

    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際のDB接続情報を設定
        db_secret = {
            # "username": "dlpf_batch",
            "username": "dlpf_ope",
            "password": "failured",
            "host": "************",
            "port": "5432",
            "dbname": "test_db",
        }
        secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))
        yield secrets


@pytest.fixture
def mock_api_secret():
    """Secrets Managerのmock - 実際のDB接続情報を使用"""
    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際のDB接続情報を設定
        api_secret = {
            "host": "https://api-test.com/",
            "client_id": "test_user",
            "client_secret": "test_secret_str",
            "token_endpoint": "token",
            "info_get_endpoint": "info",
        }
        secrets.create_secret(
            Name="api_test_secret", SecretString=json.dumps(api_secret)
        )
        yield secrets


@pytest.fixture
def db_connector(mock_secrets: Any, glue_job: GlueJobApiToFileCrm):
    glue_job.connect_db("test-db-secret")


@pytest.fixture
def db_connector_error(mock_secrets_error: Any, glue_job: GlueJobApiToFileCrm):
    glue_job.connect_db("test-db-secret")


@pytest.fixture
def glue_job():
    """GlueJobConvertCharacterEncodingのインスタンス生成"""
    return GlueJobApiToFileCrm("JN_PT001-AF01_001")


@pytest.fixture
def s3_bucket():
    """テスト用バケット名を環境変数に設定"""
    os.environ["S3_BUCKET_NAME"] = "s3-dev-dlpf-if-886436956581"
    os.environ["S3_RETRY_LIMIT"] = "3"
    os.environ["S3_RETRY_INTERVAL"] = "1"
    yield
    if "S3_BUCKET_NAME" in os.environ:
        del os.environ["S3_BUCKET_NAME"]
    if "S3_RETRY_LIMIT" in os.environ:
        del os.environ["S3_RETRY_LIMIT"]
    if "S3_RETRY_INTERVAL" in os.environ:
        del os.environ["S3_RETRY_INTERVAL"]


@pytest.fixture
def mock_parameter_store():
    """Parameter Storeのmock"""
    with mock_ssm():
        ssm = boto3.client("ssm")
        # 環境変数設定のモック - 図の通りの設定
        env_config = {
            "process": {"TZ": "Asia/Tokyo"},
            "aws": {
                "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                "S3_RETRY_LIMIT": "20",
                "S3_RETRY_INTERVAL": "1.0",
            },
        }
        ssm.put_parameter(
            Name="/glue/job/environment-config",
            Value=json.dumps(env_config),
            Type="String",
        )
        yield ssm


def test_1_get_param(db_connector, glue_job: GlueJobApiToFileCrm):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    params = get_params()
    assert params["secret_name"] == "test-db-secret"
    assert params["api_secret_name"] == "api_test_secret"
    assert params["diff_flag"]
    assert params["file_name"] == "test.csv"
    assert params["output_file_dir"] == "takahashi/input-output/POS_OUT/"
    assert params["file_setting"] == {
        "sep": ",",
        "quoting": "csv.QUOTE_ALL",
        "quotechar": '"',
        "lineterminator": "\n",
        "header": "true",
    }
    assert params["jobnet_id"] == "JN-XXXXX-XXXX"


def test_2_get_param_non_required(db_connector, glue_job: GlueJobApiToFileCrm):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # Secrets Managerシークレット名(DB接続情報)
        "test-db-secret",  # Managerシークレット名(DB接続情報)
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        False,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        # "object_key",  # レコード格納オブジェクトキー
        # "key3",  # レコード格納オブジェクトキー
        # "file_id",  # ファイル識別子
        # "test.csv"  # ファイル識別子
    ]
    try:
        get_params()
        assert True
    except ValueError:
        assert False


def test_2_get_param_file_id_required(db_connector, glue_job: GlueJobApiToFileCrm):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # Secrets Managerシークレット名(DB接続情報)
        "test-db-secret",  # Managerシークレット名(DB接続情報)
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        # "object_key",  # レコード格納オブジェクトキー
        # "key3",  # レコード格納オブジェクトキー
        # "file_id",  # ファイル識別子
        # "test.csv"  # ファイル識別子
    ]
    try:
        get_params()
        assert False
    except ValueError:
        assert True


def test_4_get_timestamp_true_success(db_connector, glue_job: GlueJobApiToFileCrm):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    with patch("source.glue_job_api_to_file_crm"):

        # mock_connector = MagicMock()
        # mock_connector.connect_db.return_value = db_connector
        # try:
        glue_job.params = get_params()
        # 2.4.1 パラメータ取得
        glue_job.diff_flag = glue_job.params["diff_flag"]
        glue_job.file_name = glue_job.params["file_name"]
        glue_job.output_file_dir = glue_job.params["output_file_dir"]
        glue_job.file_setting = glue_job.params["file_setting"]
        glue_job.jobnet_id = glue_job.params["jobnet_id"]
        glue_job.file_id = glue_job.params["file_id"]

        glue_job.get_timestamp()
        print(str(glue_job.sync_datetime))
        print(str(glue_job.now_datetime))
        assert True
    # except Exception:
    #     assert False


def test_5_get_timestamp_false_success(db_connector, glue_job: GlueJobApiToFileCrm):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        False,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    with patch("source.glue_job_api_to_file_crm"):

        # mock_connector = MagicMock()
        # mock_connector.connect_db.return_value = db_connector
        try:
            glue_job.params = get_params()
            # 2.4.1 パラメータ取得
            glue_job.diff_flag = glue_job.params["diff_flag"]
            glue_job.file_name = glue_job.params["file_name"]
            glue_job.output_file_dir = glue_job.params["output_file_dir"]
            glue_job.file_setting = glue_job.params["file_setting"]
            glue_job.jobnet_id = glue_job.params["jobnet_id"]

            glue_job.get_timestamp()
            print(glue_job.sync_datetime)
            print(glue_job.now_datetime)
            assert False
        except AttributeError:
            assert True


def test_6_get_timestamp_true_failured(
    db_connector, capsys, glue_job: GlueJobApiToFileCrm
):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_none.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    with patch("source.glue_job_api_to_file_crm"):

        # mock_connector = MagicMock()
        # mock_connector.connect_db.return_value = db_connector
        # try:
        glue_job.params = get_params()
        # 2.4.1 パラメータ取得
        glue_job.diff_flag = glue_job.params["diff_flag"]
        glue_job.file_name = glue_job.params["file_name"]
        glue_job.output_file_dir = glue_job.params["output_file_dir"]
        glue_job.file_setting = glue_job.params["file_setting"]
        glue_job.jobnet_id = glue_job.params["jobnet_id"]

        try:
            glue_job.get_timestamp()
            #     assert False
            # except Exception:
        except BaseException:
            glue_job.db_connector.rollback()
            # 2.4.12 例外処理
            glue_job.logger.error(
                "E_job_api_to_file_crm_003",
                msg_values=(traceback.format_exc()),
            )
            glue_job.logger.error(
                "E_job_api_to_file_crm_001", msg_values=(glue_job.file_name)
            )
        assert "[E_job_api_to_file_crm_003]例外発生しました。" in capsys.readouterr().out


def test_7_get_timestamp_true_db_failrured(
    db_connector_error, capsys, glue_job: GlueJobApiToFileCrm
):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # Secrets Managerシークレット名(DB接続情報)
        "test-db-secret",  # Managerシークレット名(DB接続情報)
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    with patch("source.glue_job_api_to_file_crm"):

        # mock_connector = MagicMock()
        # mock_connector.connect_db.return_value = db_connector
        # try:
        glue_job.params = get_params()
        # 2.4.1 パラメータ取得
        glue_job.diff_flag = glue_job.params["diff_flag"]
        glue_job.file_name = glue_job.params["file_name"]
        glue_job.output_file_dir = glue_job.params["output_file_dir"]
        glue_job.file_setting = glue_job.params["file_setting"]
        glue_job.jobnet_id = glue_job.params["jobnet_id"]

        try:
            glue_job.get_timestamp()
        except BaseException:
            # 2.4.12 例外処理
            glue_job.logger.error(
                "E_job_api_to_file_crm_003",
                msg_values=(traceback.format_exc()),
            )
            glue_job.logger.error(
                "E_job_api_to_file_crm_001", msg_values=(glue_job.file_name)
            )
        assert "[E_job_api_to_file_crm_003]例外発生しました。" in capsys.readouterr().out


def test_8_create_reqeuest_param_post(db_connector):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")

    glue_job.params = get_params()
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]
    glue_job.api_request_param = glue_job.params["api_request_param"]
    glue_job.api_request_method = glue_job.params["api_request_method"]
    glue_job.file_id = glue_job.params["file_id"]

    glue_job.connect_db(glue_job.params["secret_name"])

    glue_job.get_timestamp()
    glue_job.api_request_param = glue_job.get_request_param(
        param_str=glue_job.api_request_param
    )

    pprint.pprint(glue_job.api_request_param)
    assert glue_job.api_request_param


def test_9_create_reqeuest_param_get(db_connector):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "GET",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")

    glue_job.params = get_params()
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]
    glue_job.api_request_param = glue_job.params["api_request_param"]
    glue_job.api_request_method = glue_job.params["api_request_method"]
    glue_job.file_id = glue_job.params["file_id"]

    glue_job.connect_db(glue_job.params["secret_name"])

    glue_job.get_timestamp()
    glue_job.api_request_param = glue_job.get_request_param(
        param_str=glue_job.api_request_param
    )

    pprint.pprint(glue_job.api_request_param)
    assert glue_job.api_request_param


@responses.activate
def test_10_get_token_respornse_200(db_connector, mock_api_secret):
    """APIトークンを取得"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")
    glue_job.params = get_params()
    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    # mock_url = glue_job.api_info["host"] + glue_job.api_info["token_endpoint"]
    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}

    responses.add(responses.GET, mock_url, json=mock_response, status=200)
    glue_job.get_api_info()
    glue_job.get_access_token()
    assert (
        responses.calls[0].request.url
        == mock_url
        + "?grant_type=client_credentials&client_id=test_user&client_secret=test_secret_str"
    )
    assert responses.calls[0].response.json()["access_token"] == "token_success"
    assert responses.calls[0].response.status_code == 200


@responses.activate
def test_11_get_token_respornse_400(db_connector, mock_api_secret):
    """APIトークンを取得"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")
    glue_job.params = get_params()
    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    # mock_url = glue_job.api_info["host"] + glue_job.api_info["token_endpoint"]
    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}

    responses.add(responses.GET, mock_url, json=mock_response, status=400)

    glue_job.get_api_info()
    try:
        glue_job.get_access_token()
    except BaseException:
        # 2.4.12 例外処理
        glue_job.logger.error(
            "E_job_api_to_file_crm_003",
            msg_values=(traceback.format_exc()),
        )
        glue_job.logger.error("E_job_api_to_file_crm_001", msg_values=(glue_job.file_name))

    assert (
        responses.calls[0].request.url
        == mock_url
        + "?grant_type=client_credentials&client_id=test_user&client_secret=test_secret_str"
    )
    assert responses.calls[0].response.json()["access_token"] == "token_success"
    assert responses.calls[0].response.status_code == 400


@responses.activate
def test_12_get_token_respornse_200_error(db_connector, mock_api_secret, capsys):
    """APIトークンを取得"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")
    glue_job.params = get_params()
    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    # mock_url = glue_job.api_info["host"] + glue_job.api_info["token_endpoint"]
    mock_url = "https://api-test.com/other"
    mock_response = {"message": "success", "access_token": "token_success"}

    responses.add(responses.GET, mock_url, json=mock_response, status=200)
    glue_job.get_api_info()
    try:
        glue_job.get_access_token()
        assert False
    except BaseException:
        # 2.4.12 例外処理
        glue_job.logger.error(
            "E_job_api_to_file_crm_003",
            msg_values=(traceback.format_exc()),
        )
        glue_job.logger.error("E_job_api_to_file_crm_001", msg_values=(glue_job.file_name))

    assert (
        responses.calls[0].request.url
        # == mock_url
        == "https://api-test.com/token"
        + "?grant_type=client_credentials&client_id=test_user&client_secret=test_secret_str"
    )


@responses.activate
def test_13_execute_api_respornse_200(db_connector, mock_api_secret):
    """APIトークンを取得"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")
    glue_job.params = get_params()
    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]
    glue_job.api_request_param = glue_job.params["api_request_param"]
    glue_job.api_request_method = glue_job.params["api_request_method"]

    glue_job.token = "token_success"
    # mock_url = glue_job.api_info["host"] + glue_job.api_info["token_endpoint"]
    mock_url = "https://api-test.com/info"
    mock_response = {"message": "success", "result": "data1"}

    responses.add(responses.POST, mock_url, json=mock_response, status=200)
    glue_job.get_api_info()

    glue_job.request_param = "PT001_param"
    glue_job.execute_api()
    assert responses.calls[0].request.url == mock_url
    assert responses.calls[0].response.json()["result"] == "data1"
    assert responses.calls[0].response.status_code == 200
    assert responses.calls[0].request.headers["Authorization"] == "Bearer token_success"


@responses.activate
def test_14_execute_api_respornse_200_result_none(db_connector, mock_api_secret):
    """APIトークンを取得"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")
    glue_job.params = get_params()
    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]
    glue_job.api_request_param = glue_job.params["api_request_param"]
    glue_job.api_request_method = glue_job.params["api_request_method"]

    glue_job.token = "token_success"
    # mock_url = glue_job.api_info["host"] + glue_job.api_info["token_endpoint"]
    mock_url = "https://api-test.com/info"
    mock_response = {}

    responses.add(responses.POST, mock_url, json=mock_response, status=200)
    glue_job.get_api_info()

    glue_job.request_param = "PT001_param"
    glue_job.execute_api()
    assert responses.calls[0].request.url == mock_url
    assert responses.calls[0].response.json() == {}
    assert responses.calls[0].response.status_code == 200
    assert responses.calls[0].request.headers["Authorization"] == "Bearer token_success"


@responses.activate
def test_15_execute_api_respornse_401(db_connector, mock_api_secret):
    """APIトークンを取得"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")
    glue_job.params = get_params()
    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]
    glue_job.api_request_param = glue_job.params["api_request_param"]
    glue_job.api_request_method = glue_job.params["api_request_method"]

    glue_job.token = "token_success"
    # mock_url = glue_job.api_info["host"] + glue_job.api_info["token_endpoint"]
    mock_url = "https://api-test.com/info"
    mock_response = {"message": "success", "result": "data1"}

    responses.add(responses.POST, mock_url, json=mock_response, status=401)
    glue_job.get_api_info()

    glue_job.request_param = "PT001_param"
    try:
        glue_job.execute_api()

    except BaseException:
        # 2.4.12 例外処理
        glue_job.logger.error(
            "E_job_api_to_file_crm_003",
            msg_values=(traceback.format_exc()),
        )
        glue_job.logger.error("E_job_api_to_file_crm_001", msg_values=(glue_job.file_name))

    assert responses.calls[0].request.url == mock_url
    assert responses.calls[0].response.json()["result"] == "data1"
    assert responses.calls[0].response.status_code == 401


@responses.activate
def test_16_execute_api_respornse_200_error(db_connector, mock_api_secret):
    """APIトークンを取得"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")
    glue_job.params = get_params()
    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]
    glue_job.api_request_param = glue_job.params["api_request_param"]
    glue_job.api_request_method = glue_job.params["api_request_method"]

    glue_job.token = "token_success"
    # mock_url = glue_job.api_info["host"] + glue_job.api_info["token_endpoint"]
    mock_url = "https://api-test.com/info"
    mock_response = {"message": "success", "result": "data1"}

    responses.add(responses.GET, mock_url, json=mock_response, status=401)
    glue_job.get_api_info()

    glue_job.request_param = "PT001_param"
    try:
        glue_job.execute_api()
        assert False
    except BaseException:
        # 2.4.12 例外処理
        glue_job.logger.error(
            "E_job_api_to_file_crm_003",
            msg_values=(traceback.format_exc()),
        )
        glue_job.logger.error("E_job_api_to_file_crm_001", msg_values=(glue_job.file_name))

    assert (
        responses.calls[0].request.url
        # == mock_url
        == "https://api-test.com/info"
    )


@responses.activate
def test_for_coverage_execute_api_param_invalid(db_connector, mock_api_secret, capsys):
    """APIトークンを取得"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "INVALID",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")
    glue_job.params = get_params()
    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]
    glue_job.api_request_param = glue_job.params["api_request_param"]
    glue_job.api_request_method = glue_job.params["api_request_method"]

    glue_job.token = "token_success"
    # mock_url = glue_job.api_info["host"] + glue_job.api_info["token_endpoint"]
    mock_url = "https://api-test.com/info"
    mock_response = {"message": "success", "result": "data1"}

    responses.add(responses.GET, mock_url, json=mock_response, status=401)
    glue_job.get_api_info()

    glue_job.request_param = "PT001_param"
    try:
        glue_job.execute_api()
        assert False
    except BaseException:
        # 2.4.12 例外処理
        glue_job.logger.error(
            "E_job_api_to_file_crm_003",
            msg_values=(traceback.format_exc()),
        )
        glue_job.logger.error("E_job_api_to_file_crm_001", msg_values=(glue_job.file_name))

    assert "E_job_api_to_file_crm_001" in capsys.readouterr().out


# def test_17_edit_response(db_connector):
#     """ログを取得する"""
#     sys.argv = [
#         "glue_job_api_to_file_crm",
#         "TZ",
#         "Asia/Tokyo",
#         "enable-job-insights",
#         "false",
#         "enable-glue-datacatalog",
#         "true",
#         "library-set",
#         "analytics",
#         "python-version",
#         "3.9",
#         "job-language",
#         "python",
#         "fila_data",
#         "aaa,data1,111\r\nbbb,data2,222",
#         "secret_name",  # DBシークレット名
#         "test-db-secret",  # DBシークレット名
#         "api_secret_name",  # API　Secrets Managerシークレット名
#         "api_test_secret",  # API　Secrets Managerシークレット名
#         "diff_flag",  # 差分連携フラグ
#         True,  # 差分連携フラグ
#         "file_name",  # ファイル名
#         "test.csv",  # ファイル名
#         "output_file_dir",  # アウトプットファイルディレクトリ
#         "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
#         "file_setting",  # 出力ファイル属性
#         {
#             "sep": ",",
#             "quoting": "csv.QUOTE_ALL",
#             "quotechar": '"',
#             "lineterminator": "\n",
#             "header": "true",
#         },  # 出力ファイル属性
#         "jobnet_id",  # ジョブネットID
#         "JN_PT001-AF01_001",  # ジョブネットID
#         "api_request_param",  # 情報取得APIリクエストパラメータ
#         '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
#         "api_request_method",  # 情報取得APIリクエストメソッド
#         "POST",  # 情報取得APIリクエストメソッド
#         "records_key",  # レコード特定用キー
#         "rec1",  # レコード特定用キー
#         "output_keys",  # 出力対象キー
#         "key2",  # 出力対象キー
#         "object_key",  # レコード格納オブジェクトキー
#         "key3",  # レコード格納オブジェクトキー
# "file_id",  # ファイル識別子
# "test.csv"  # ファイル識別子
#     ]
#     glue_job = GlueJobApiToFileCrm("JN_PT001-AF01_001")
#     params = get_params()
#     glue_job.edit_response()
#     assert glue_job.response == "PT001_dummy_response"


# def test_18_edit_response_others(db_connector):
#     """ログを取得する"""
#     sys.argv = [
#         "glue_job_api_to_file_crm",
#         "TZ",
#         "Asia/Tokyo",
#         "enable-job-insights",
#         "false",
#         "enable-glue-datacatalog",
#         "true",
#         "library-set",
#         "analytics",
#         "python-version",
#         "3.9",
#         "job-language",
#         "python",
#         "fila_data",
#         "aaa,data1,111\r\nbbb,data2,222",
#         "secret_name",  # DBシークレット名
#         "test-db-secret",  # DBシークレット名
#         "api_secret_name",  # API　Secrets Managerシークレット名
#         "api_test_secret",  # API　Secrets Managerシークレット名
#         "diff_flag",  # 差分連携フラグ
#         True,  # 差分連携フラグ
#         "file_name",  # ファイル名
#         "test.csv",  # ファイル名
#         "output_file_dir",  # アウトプットファイルディレクトリ
#         "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
#         "file_setting",  # 出力ファイル属性
#         {
#             "sep": ",",
#             "quoting": "csv.QUOTE_ALL",
#             "quotechar": '"',
#             "lineterminator": "\n",
#             "header": "true",
#         },  # 出力ファイル属性
#         "jobnet_id",  # ジョブネットID
#         "JN-XXXXX-XXXX",  # ジョブネットID
#         "api_request_param",  # 情報取得APIリクエストパラメータ
#         '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
#         "api_request_method",  # 情報取得APIリクエストメソッド
#         "POST",  # 情報取得APIリクエストメソッド
#         "records_key",  # レコード特定用キー
#         "rec1",  # レコード特定用キー
#         "output_keys",  # 出力対象キー
#         "key2",  # 出力対象キー
#         "object_key",  # レコード格納オブジェクトキー
#         "key3",  # レコード格納オブジェクトキー
# "file_id",  # ファイル識別子
# "test.csv"  # ファイル識別子
#     ]
#     glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
#     params = get_params()
#     glue_job.edit_response()
#     assert len(glue_job.df) == 0


def test_edit_response_no_object_key(glue_job: GlueJobApiToFileCrm):
    # レコード格納オブジェクトキーを存在しないキーにしたとき、空で返却されること

    # 事前データ設定
    response_dict = {
        "data": {
            "sample1": "data1",
            "sample2": [
                {"sample2-1-1": "data2-1-1", "sample2-1-2": "data2-1-2"},
                {"sample2-2-1": "data2-2-1", "sample2-2-2": "data2-2-2"},
            ],
            "sample3": {"sample3-1": "data3-1", "sample3-2": "data3-2"},
        }
    }
    glue_job.response = json.dumps(response_dict, indent=4)
    glue_job.object_key = "datax"

    # 実行
    df = glue_job.edit_response()

    # 確認
    assert df.empty


def test_edit_response_dict_no_record_key(glue_job: GlueJobApiToFileCrm):
    # レコード格納オブジェクトキーを存在するキー、レコード特定用キーを存在しないキー、
    # かつレコード格納オブジェクトがリスト以外のとき、空で返却されること

    # 事前データ設定
    response_dict = {
        "data": {
            "sample1": "data1",
            "sample2": [
                {"sample2-1-1": "data2-1-1", "sample2-1-2": "data2-1-2"},
                {"sample2-2-1": "data2-2-1", "sample2-2-2": "data2-2-2"},
            ],
            "sample3": {"sample3-1": "data3-1", "sample3-2": "data3-2"},
        }
    }
    glue_job.response = json.dumps(response_dict, indent=4)
    glue_job.object_key = "data"
    glue_job.records_key = "sample"

    # 実行
    df = glue_job.edit_response()

    # 確認
    assert df.empty


def test_edit_response_list_no_record_key(glue_job: GlueJobApiToFileCrm):
    # レコード格納オブジェクトキーを存在するキー、レコード特定用キーを存在しないキー、
    # かつレコード格納オブジェクトがリストのとき、空で返却されること

    # 事前データ設定
    response_dict = {"data": [{"sample1": "data1"}]}
    glue_job.response = json.dumps(response_dict, indent=4)
    glue_job.object_key = "data"
    glue_job.records_key = "sample"

    # 実行
    df = glue_job.edit_response()

    # 確認
    assert df.empty


def test_edit_response_without_object_key(glue_job: GlueJobApiToFileCrm):
    # レコード格納オブジェクトキーを指定なし、レコード特定用キーを存在するキー、
    # かつレコード格納オブジェクトがリスト以外のとき、想定通りの値で返却されること

    # 事前データ設定
    response_dict = [
        {
            "sample2-1": "data2-1-1",
            "sample2-2": "data2-1-2",
            "sample2-3": "data2-1-3",
        },
        {
            "sample2-1": "data2-2-1",
            "sample2-2": "data2-2-2",
            "sample2-3": "data2-2-3",
        },
    ]
    glue_job.response = json.dumps(response_dict, indent=4)
    glue_job.object_key = None
    glue_job.records_key = "sample2-1"
    glue_job.output_keys = ["sample2-1", "sample2-2"]

    # 実行
    actual_df = glue_job.edit_response()

    # 確認
    expected_df = pd.DataFrame(
        {
            "sample2-1": ["data2-1-1", "data2-2-1"],
            "sample2-2": ["data2-1-2", "data2-2-2"],
        }
    )
    assert actual_df.index.equals(expected_df.index)
    assert actual_df.columns.equals(expected_df.columns)
    assert actual_df.equals(expected_df)


def test_edit_response_without_object_key_2(glue_job: GlueJobApiToFileCrm):
    # レコード格納オブジェクトキーを指定なし、レコード特定用キーを存在するキー、
    # かつレコード格納オブジェクトがリスト以外のとき、想定通りの値で返却されること

    # 事前データ設定
    response_dict = (
        {
            "sample2-1": "data2-1-1",
            "sample2-2": "data2-1-2",
            "sample2-3": "data2-1-3",
        },
    )
    glue_job.response = json.dumps(response_dict, indent=4)
    glue_job.object_key = None
    glue_job.records_key = "sample2-1"
    glue_job.output_keys = ["sample2-1", "sample2-2"]

    # 実行
    actual_df = glue_job.edit_response()

    # 確認
    expected_df = pd.DataFrame(
        {
            "sample2-1": ["data2-1-1"],
            "sample2-2": ["data2-1-2"],
        }
    )
    assert actual_df.index.equals(expected_df.index)
    assert actual_df.columns.equals(expected_df.columns)
    assert actual_df.equals(expected_df)


def test_edit_response_with_object_key(glue_job: GlueJobApiToFileCrm):
    # レコード格納オブジェクトキーを指定あり、レコード特定用キーを存在するキー、
    # かつレコード格納オブジェクトがリストのとき、想定通りの値で返却されること

    # 事前データ設定
    response_dict = {
        "sample1": "data1",
        "sample2": [
            {
                "sample2-1": "data2-1-1",
                "sample2-2": "data2-1-2",
                "sample2-3": "data2-1-3",
            },
            {
                "sample2-1": "data2-2-1",
                "sample2-2": "data2-2-2",
                "sample2-3": "data2-2-3",
            },
        ],
        "sample3": {"sample3-1": "data3-1", "sample3-2": "data3-2"},
    }
    glue_job.response = json.dumps(response_dict, indent=4)
    glue_job.object_key = "sample2"
    glue_job.records_key = "sample2-1"
    glue_job.output_keys = ["sample2-1", "sample2-2"]

    # 実行
    actual_df = glue_job.edit_response()

    # 確認
    expected_df = pd.DataFrame(
        {
            "sample2-1": ["data2-1-1", "data2-2-1"],
            "sample2-2": ["data2-1-2", "data2-2-2"],
        }
    )
    assert actual_df.index.equals(expected_df.index)
    assert actual_df.columns.equals(expected_df.columns)
    assert actual_df.equals(expected_df)


def test_23_put_to_s3_success(s3_bucket, glue_job):
    """ログを取得する"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_ALL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
    glue_job.params = get_params()

    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    df = pd.DataFrame(
        {
            "c1": ["First", "Second", "Third"],
            "c2": ["""テスト1""", """テスト3""", """テスト5"""],
            "c3": ["""テスト2""", """テスト4""", """テスト6"""],
        }
    ).set_index("c1")

    glue_job.df = df
    glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)
    glue_job.put_to_s3(
        file_data=glue_job.file_data,
        output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    file_data = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    assert (
        file_data["Body"].read().decode("utf-8")
        == '"テスト1","テスト2"\n"テスト3","テスト4"\n"テスト5","テスト6"\n'
    )


def test_20_edit_file_to_tsv(s3_bucket, glue_job):
    """ログを取得する"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.tsv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"\\t" , "quoting": "csv.QUOTE_NONE","lineterminator": "\\r\\n","header": "False","index":"False","encoding":"cp932"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
    glue_job.params = get_params()

    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    import locale

    print(sys.getdefaultencoding)
    print(locale.getpreferredencoding)
    df = pd.DataFrame(
        {
            "c1": ["First", "Second", "Third"],
            "c2": ["""テスト1""", """テスト3""", """テスト5"""],
            "c3": ["""テスト2""", """テスト4""", """テスト6"""],
        }
    ).set_index("c1")

    glue_job.df = df
    glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)

    glue_job.put_to_s3(
        file_data=glue_job.file_data,
        output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    file_data = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    assert (
        file_data["Body"].read().decode("cp932")
        == "テスト1\tテスト2\r\nテスト3\tテスト4\r\nテスト5\tテスト6\r\n"
    )


def test_24_put_to_s3_success_0byte(s3_bucket, glue_job):
    """ログを取得する"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_0byte.tsv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"\\t" , "quoting": "csv.QUOTE_NONE","lineterminator": "\\r\\n","header": "False","index":"False","encoding":"cp932"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
    glue_job.params = get_params()

    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    glue_job.df = pd.DataFrame()
    glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)
    glue_job.put_to_s3(
        file_data=glue_job.file_data,
        output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    file_data = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    assert file_data["Body"].read().decode("utf-8") == ""


def test_22_edit_file_to_csv_dummy_option(s3_bucket, glue_job):
    """ログを取得する"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.tsv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"\\t" , "quoting": "DUMMY","lineterminator": "\\r\\n","header": "False","encoding":"DUMMY"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
    glue_job.params = get_params()

    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    df = pd.DataFrame(
        {
            "c1": ["First", "Second", "Third"],
            "c2": ["""テスト1""", """テスト3""", """テスト5"""],
            "c3": ["""テスト2""", """テスト4""", """テスト6"""],
        }
    ).set_index("c1")

    try:
        glue_job.df = df
        glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)
        glue_job.put_to_s3(
            file_data=glue_job.file_data,
            output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
        )
        glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
        )
        assert False

    except BaseException:
        # 2.4.12 例外処理
        glue_job.logger.error(
            "E_job_api_to_file_crm_003",
            msg_values=(traceback.format_exc()),
        )
        glue_job.logger.error("E_job_api_to_file_crm_001", msg_values=(glue_job.file_name))
        assert True


def test_25_put_to_s3_failed(s3_bucket, glue_job, capsys):
    """ログを取得する"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_failured.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_ALL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
    glue_job.params = get_params()

    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    df = pd.DataFrame(
        {
            "c1": ["First", "Second", "Third"],
            "c2": ["""テスト1""", """テスト3""", """テスト5"""],
            "c3": ["""テスト2""", """テスト4""", """テスト6"""],
        }
    ).set_index("c1")

    glue_job.df = df
    glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)

    # レスポンス作成
    response = glue_job.s3_client.put_object(
        Body=glue_job.file_data,
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    # テストファイル削除(レスポンス作成するがファイルは削除)
    glue_job.s3_client.delete_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )

    with patch.object(
        glue_job.s3_client,
        "put_object",
        side_effect=[
            Exception("Test Exception"),
            Exception("Test Exception"),
            Exception("Test Exception"),
            response,  # 3回以上失敗
        ],
    ):
        try:
            glue_job.put_to_s3(
                file_data=glue_job.file_data,
                output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
            )
            assert False
        except Exception:
            assert (
                "[E_job_api_to_file_crm_002]処理で異常が発生しました。(処理名=S3ファイル配置)"
                in capsys.readouterr().out
            )


def test_26_put_to_s3_retry_success(s3_bucket, glue_job, capsys):
    """ログを取得する"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_retry_success.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_ALL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
    glue_job.params = get_params()

    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    df = pd.DataFrame(
        {
            "c1": ["First", "Second", "Third"],
            "c2": ["""テスト1""", """テスト3""", """テスト5"""],
            "c3": ["""テスト2""", """テスト4""", """テスト6"""],
        }
    ).set_index("c1")

    glue_job.df = df
    glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)

    # レスポンス作成
    response = glue_job.s3_client.put_object(
        Body=glue_job.file_data,
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )

    with patch.object(
        glue_job.s3_client,
        "put_object",
        side_effect=[
            Exception("Test Exception"),
            response,  # 2回目で成功
        ],
    ):
        glue_job.put_to_s3(
            file_data=glue_job.file_data,
            output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
        )

        file_data = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
        )

        assert (
            file_data["Body"].read().decode("utf-8")
            == '"テスト1","テスト2"\n"テスト3","テスト4"\n"テスト5","テスト6"\n'
        )


def test_27_update_timestamp_true_success(db_connector, glue_job: GlueJobApiToFileCrm):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    with patch("source.glue_job_api_to_file_crm"):

        # mock_connector = MagicMock()
        # mock_connector.connect_db.return_value = db_connector
        # try:
        glue_job.params = get_params()
        # 2.4.1 パラメータ取得
        glue_job.diff_flag = glue_job.params["diff_flag"]
        glue_job.file_name = glue_job.params["file_name"]
        glue_job.output_file_dir = glue_job.params["output_file_dir"]
        glue_job.file_setting = glue_job.params["file_setting"]
        glue_job.jobnet_id = glue_job.params["jobnet_id"]
        glue_job.file_id = glue_job.params["file_id"]

        glue_job.db_connector.begin()
        glue_job.get_timestamp()
        glue_job.update_timestamp()
        glue_job.db_connector.commit()

        print(str(glue_job.sync_datetime))
        print(str(glue_job.now_datetime))
        assert True

    # except Exception:
    #     assert False


def test_28_update_timestamp_false_success(db_connector, glue_job: GlueJobApiToFileCrm):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        False,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    with patch("source.glue_job_api_to_file_crm"):

        # mock_connector = MagicMock()
        # mock_connector.connect_db.return_value = db_connector
        try:
            glue_job.params = get_params()
            # 2.4.1 パラメータ取得
            glue_job.diff_flag = glue_job.params["diff_flag"]
            glue_job.file_name = glue_job.params["file_name"]
            glue_job.output_file_dir = glue_job.params["output_file_dir"]
            glue_job.file_setting = glue_job.params["file_setting"]
            glue_job.jobnet_id = glue_job.params["jobnet_id"]

            glue_job.db_connector.begin()
            glue_job.get_timestamp()
            glue_job.update_timestamp()
            glue_job.db_connector.commit()

            print(glue_job.sync_datetime)
            print(glue_job.now_datetime)
            assert False
        except AttributeError:
            glue_job.db_connector.rollback()
            assert True
        finally:
            glue_job.db_connector.close()


def test_29_update_timestamp_true_failured(
    db_connector, capsys, glue_job: GlueJobApiToFileCrm
):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    with patch("source.glue_job_api_to_file_crm"):

        # mock_connector = MagicMock()
        # mock_connector.connect_db.return_value = db_connector
        # try:
        glue_job.params = get_params()
        # 2.4.1 パラメータ取得
        glue_job.diff_flag = glue_job.params["diff_flag"]
        glue_job.file_name = glue_job.params["file_name"]
        glue_job.output_file_dir = glue_job.params["output_file_dir"]
        glue_job.file_setting = glue_job.params["file_setting"]
        glue_job.jobnet_id = glue_job.params["jobnet_id"]

        try:

            glue_job.update_timestamp()

            #     assert False
            # except Exception:
        except BaseException:
            # 2.4.12 例外処理
            glue_job.logger.error(
                "E_job_api_to_file_crm_003",
                msg_values=(traceback.format_exc()),
            )
            glue_job.logger.error(
                "E_job_api_to_file_crm_001", msg_values=(glue_job.file_name)
            )
        assert "[E_job_api_to_file_crm_003]例外発生しました。" in capsys.readouterr().out


def test_for_coverage_edit_file_to_csv_QUOTE_MINIMAL(s3_bucket, glue_job):
    """ログを取得する"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_for_coverage.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_MINIMAL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
    glue_job.params = get_params()

    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    df = pd.DataFrame(
        {
            "c1": ["First", "Second", "Third"],
            "c2": ["""テスト1""", """テスト3""", """テスト5"""],
            "c3": ["""テスト2""", """テスト4""", """テスト6"""],
        }
    ).set_index("c1")

    glue_job.df = df
    glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)
    glue_job.put_to_s3(
        file_data=glue_job.file_data,
        output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    file_data = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    assert (
        file_data["Body"].read().decode("utf-8")
        == "テスト1,テスト2\nテスト3,テスト4\nテスト5,テスト6\n"
    )


def test_for_coverage_edit_file_to_csv_QUOTE_NONNUMERIC(s3_bucket, glue_job):
    """ログを取得する"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_for_coverage.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_NONNUMERIC","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
    glue_job.params = get_params()

    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    df = pd.DataFrame(
        {
            "c1": ["First", "Second", "Third"],
            "c2": ["""テスト1""", """テスト3""", """テスト5"""],
            "c3": ["""テスト2""", """テスト4""", """テスト6"""],
        }
    ).set_index("c1")

    glue_job.df = df
    glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)
    glue_job.put_to_s3(
        file_data=glue_job.file_data,
        output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    file_data = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    assert (
        file_data["Body"].read().decode("utf-8")
        == '"テスト1","テスト2"\n"テスト3","テスト4"\n"テスト5","テスト6"\n'
    )


# ※Python3.9では存在しないオプションのためコメントアウト
# def test_for_coverage_edit_file_to_csv_QUOTE_NOTNULL(s3_bucket, glue_job):
#     """ログを取得する"""

#     sys.argv = [
#         "glue_job_api_to_file_crm",
#         "TZ",
#         "Asia/Tokyo",
#         "enable-job-insights",
#         "false",
#         "enable-glue-datacatalog",
#         "true",
#         "library-set",
#         "analytics",
#         "python-version",
#         "3.9",
#         "job-language",
#         "python",
#         "fila_data",
#         "aaa,data1,111\r\nbbb,data2,222",
#         "secret_name",  # DBシークレット名
#         "test-db-secret",  # DBシークレット名
#         "api_secret_name",  # API　Secrets Managerシークレット名
#         "api_test_secret",  # API　Secrets Managerシークレット名
#         "diff_flag",  # 差分連携フラグ
#         True,  # 差分連携フラグ
#         "file_name",  # ファイル名
#         "test_for_coverage.csv",  # ファイル名
#         "output_file_dir",  # アウトプットファイルディレクトリ
#         "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
#         "file_setting",  # 出力ファイル属性
#         '{"sep":"," , "quoting": "csv.QUOTE_NOTNULL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
#         "jobnet_id",  # ジョブネットID
#         "JN-XXXXX-XXXX",  # ジョブネットID
#         "api_request_param",  # 情報取得APIリクエストパラメータ
#         '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
#         "api_request_method",  # 情報取得APIリクエストメソッド
#         "POST",  # 情報取得APIリクエストメソッド
#         "records_key",  # レコード特定用キー
#         "rec1",  # レコード特定用キー
#         "output_keys",  # 出力対象キー
#         "key2",  # 出力対象キー
#         "object_key",  # レコード格納オブジェクトキー
#         "key3",  # レコード格納オブジェクトキー
# "file_id",  # ファイル識別子
# "test.csv"  # ファイル識別子
#     ]
#     # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
#     glue_job.params = get_params()

#     # 2.4.1 パラメータ取得
#     glue_job.api_secret_name = glue_job.params["api_secret_name"]
#     glue_job.diff_flag = glue_job.params["diff_flag"]
#     glue_job.file_name = glue_job.params["file_name"]
#     glue_job.output_file_dir = glue_job.params["output_file_dir"]
#     glue_job.file_setting = glue_job.params["file_setting"]
#     glue_job.jobnet_id = glue_job.params["jobnet_id"]

#     df = pd.DataFrame(
#         {
#             "c1": ["First", "Second", "Third"],
#             "c2": ["""テスト1""", """テスト3""", """テスト5"""],
#             "c3": ["""テスト2""", """テスト4""", """テスト6"""],
#         }
#     ).set_index("c1")

#     glue_job.df = df
#     glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)
#     glue_job.put_to_s3(
#         file_data=glue_job.file_data,
#         output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
#     )
#     file_data = glue_job.s3_client.get_object(
#         Bucket=os.environ["S3_BUCKET_NAME"],
#         Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
#     )
#     assert (
#         file_data["Body"].read().decode("utf-8")
#         == '"テスト1","テスト2"\n"テスト3","テスト4"\n"テスト5","テスト6"\n'
#     )

# ※Python3.9では存在しないオプションのためコメントアウト
# def test_for_coverage_edit_file_to_csv_QUOTE_STRINGS(s3_bucket, glue_job):
#     """ログを取得する"""

#     sys.argv = [
#         "glue_job_api_to_file_crm",
#         "TZ",
#         "Asia/Tokyo",
#         "enable-job-insights",
#         "false",
#         "enable-glue-datacatalog",
#         "true",
#         "library-set",
#         "analytics",
#         "python-version",
#         "3.9",
#         "job-language",
#         "python",
#         "fila_data",
#         "aaa,data1,111\r\nbbb,data2,222",
#         "secret_name",  # DBシークレット名
#         "test-db-secret",  # DBシークレット名
#         "api_secret_name",  # API　Secrets Managerシークレット名
#         "api_test_secret",  # API　Secrets Managerシークレット名
#         "diff_flag",  # 差分連携フラグ
#         True,  # 差分連携フラグ
#         "file_name",  # ファイル名
#         "test_for_coverage.csv",  # ファイル名
#         "output_file_dir",  # アウトプットファイルディレクトリ
#         "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
#         "file_setting",  # 出力ファイル属性
#         '{"sep":"," , "quoting": "csv.QUOTE_STRINGS","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
#         "jobnet_id",  # ジョブネットID
#         "JN-XXXXX-XXXX",  # ジョブネットID
#         "api_request_param",  # 情報取得APIリクエストパラメータ
#         '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
#         "api_request_method",  # 情報取得APIリクエストメソッド
#         "POST",  # 情報取得APIリクエストメソッド
#         "records_key",  # レコード特定用キー
#         "rec1",  # レコード特定用キー
#         "output_keys",  # 出力対象キー
#         "key2",  # 出力対象キー
#         "object_key",  # レコード格納オブジェクトキー
#         "key3",  # レコード格納オブジェクトキー
# "file_id",  # ファイル識別子
# "test.csv"  # ファイル識別子
#     ]
#     # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
#     glue_job.params = get_params()

#     # 2.4.1 パラメータ取得
#     glue_job.api_secret_name = glue_job.params["api_secret_name"]
#     glue_job.diff_flag = glue_job.params["diff_flag"]
#     glue_job.file_name = glue_job.params["file_name"]
#     glue_job.output_file_dir = glue_job.params["output_file_dir"]
#     glue_job.file_setting = glue_job.params["file_setting"]
#     glue_job.jobnet_id = glue_job.params["jobnet_id"]

#     df = pd.DataFrame(
#         {
#             "c1": ["First", "Second", "Third"],
#             "c2": ["""テスト1""", """テスト3""", """テスト5"""],
#             "c3": ["""テスト2""", """テスト4""", """テスト6"""],
#         }
#     ).set_index("c1")

#     glue_job.df = df
#     glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)
#     glue_job.put_to_s3(
#         file_data=glue_job.file_data,
#         output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
#     )
#     file_data = glue_job.s3_client.get_object(
#         Bucket=os.environ["S3_BUCKET_NAME"],
#         Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
#     )
#     assert (
#         file_data["Body"].read().decode("utf-8")
#         == '"テスト1","テスト2"\n"テスト3","テスト4"\n"テスト5","テスト6"\n'
#     )


def test_for_coverage_edit_file_to_header_none(s3_bucket, glue_job):
    """ログを取得する"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_for_coverage.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_ALL","quotechar": "\\"" ,"lineterminator": "\\n","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]
    # glue_job = GlueJobApiToFileCrm("JN-XXXXX-XXXX")
    glue_job.params = get_params()

    # 2.4.1 パラメータ取得
    glue_job.api_secret_name = glue_job.params["api_secret_name"]
    glue_job.diff_flag = glue_job.params["diff_flag"]
    glue_job.file_name = glue_job.params["file_name"]
    glue_job.output_file_dir = glue_job.params["output_file_dir"]
    glue_job.file_setting = glue_job.params["file_setting"]
    glue_job.jobnet_id = glue_job.params["jobnet_id"]

    df = pd.DataFrame(
        {
            "c1": ["First", "Second", "Third"],
            "c2": ["""テスト1""", """テスト3""", """テスト5"""],
            "c3": ["""テスト2""", """テスト4""", """テスト6"""],
        }
    ).set_index("c1")

    glue_job.df = df
    glue_job.file_data = glue_job.to_csv(file_setting=glue_job.file_setting)
    glue_job.put_to_s3(
        file_data=glue_job.file_data,
        output_path=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    file_data = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=os.path.join(glue_job.output_file_dir + glue_job.file_name),
    )
    assert (
        file_data["Body"].read().decode("utf-8")
        == '"c2","c3"\n"テスト1","テスト2"\n"テスト3","テスト4"\n"テスト5","テスト6"\n'
    )


def test_for_coverage_get_param_required_less(db_connector, glue_job: GlueJobApiToFileCrm):
    """ログを取得する"""
    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # DBシークレット名
        "test-db-secret",  # DBシークレット名
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": "true",
        },  # 出力ファイル属性
        # "jobnet_id",  # ジョブネットID
        # "JN-XXXXX-XXXX",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test",  # ファイル識別子
    ]

    try:
        get_params()
    except ValueError:
        assert True


@responses.activate
def test_main_post(mock_secrets, mock_api_secret, mock_parameter_store, capsys):
    """メイン"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # Secrets Managerシークレット名(DB接続情報)
        "test-db-secret",  # Managerシークレット名(DB接続情報)
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_main.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_ALL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "sample2-1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        ["sample2-1", "sample2-2"],  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test_main",  # ファイル識別子
    ]

    secrets = boto3.client("secretsmanager")
    # 実際のDB接続情報を設定
    db_secret = {
        # "username": "dlpf_batch",
        "username": "dlpf_ope",
        "password": "password",
        "host": "************",
        "port": "5432",
        "dbname": "test_db",
    }
    secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))

    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}
    responses.add(responses.GET, mock_url, json=mock_response, status=200)
    mock_url2 = "https://api-test.com/info"
    mock_response2 = {
        "data": {
            "sample1": "data1",
            "sample2": [
                {"sample2-1-1": "data2-1-1", "sample2-1-2": "data2-1-2"},
                {"sample2-2-1": "data2-2-1", "sample2-2-2": "data2-2-2"},
            ],
            "sample3": {"sample3-1": "data3-1", "sample3-2": "data3-2"},
        }
    }
    mock_response2 = json.dumps(mock_response2, indent=4)
    responses.add(responses.POST, mock_url2, json=mock_response2, status=200)

    with patch("source.glue_job_api_to_file_crm.GlueJobApiToFileCrm.put_to_s3"):

        try:
            main()
        except SystemExit:
            assert False

        assert not (
            "[E_job_api_to_file_crm_003]例外発生しました。" in capsys.readouterr().out
        )


@responses.activate
def test_main_post_diff_flag_false(
    mock_secrets, mock_api_secret, mock_parameter_store, capsys
):
    """メイン"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # Secrets Managerシークレット名(DB接続情報)
        "test-db-secret",  # Managerシークレット名(DB接続情報)
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        False,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_main.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_ALL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "sample2-1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        ["sample2-1", "sample2-2"],  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test_main",  # ファイル識別子
    ]

    secrets = boto3.client("secretsmanager")
    # 実際のDB接続情報を設定
    db_secret = {
        # "username": "dlpf_batch",
        "username": "dlpf_ope",
        "password": "password",
        "host": "************",
        "port": "5432",
        "dbname": "test_db",
    }
    secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))

    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}
    responses.add(responses.GET, mock_url, json=mock_response, status=200)
    mock_url2 = "https://api-test.com/info"
    mock_response2 = {
        "data": {
            "sample1": "data1",
            "sample2": [
                {"sample2-1-1": "data2-1-1", "sample2-1-2": "data2-1-2"},
                {"sample2-2-1": "data2-2-1", "sample2-2-2": "data2-2-2"},
            ],
            "sample3": {"sample3-1": "data3-1", "sample3-2": "data3-2"},
        }
    }
    mock_response2 = json.dumps(mock_response2, indent=4)
    responses.add(responses.POST, mock_url2, json=mock_response2, status=200)

    with patch("source.glue_job_api_to_file_crm.GlueJobApiToFileCrm.put_to_s3"):

        try:
            main()
        except SystemExit:
            assert False

        assert not (
            "[E_job_api_to_file_crm_003]例外発生しました。" in capsys.readouterr().out
        )


@responses.activate
def test_main_post_without_object_key(
    mock_secrets, mock_api_secret, mock_parameter_store, capsys
):
    """メイン"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # Secrets Managerシークレット名(DB接続情報)
        "test-db-secret",  # Managerシークレット名(DB接続情報)
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_main.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_ALL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "sample2-1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        ["sample2-1", "sample2-2"],  # 出力対象キー
        # "object_key",  # レコード格納オブジェクトキー
        # "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test_main",  # ファイル識別子
    ]

    secrets = boto3.client("secretsmanager")
    # 実際のDB接続情報を設定
    db_secret = {
        # "username": "dlpf_batch",
        "username": "dlpf_ope",
        "password": "password",
        "host": "************",
        "port": "5432",
        "dbname": "test_db",
    }
    secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))

    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}
    responses.add(responses.GET, mock_url, json=mock_response, status=200)
    mock_url2 = "https://api-test.com/info"
    mock_response2 = [
        {
            "sample2-1": "data2-1-1",
            "sample2-2": "data2-1-2",
            "sample2-3": "data2-1-3",
        },
        {
            "sample2-1": "data2-2-1",
            "sample2-2": "data2-2-2",
            "sample2-3": "data2-2-3",
        },
    ]

    mock_response2 = json.dumps(mock_response2, indent=4)
    responses.add(responses.POST, mock_url2, json=mock_response2, status=200)

    with patch("source.glue_job_api_to_file_crm.GlueJobApiToFileCrm.put_to_s3"):
        try:
            main()
        except SystemExit:
            assert False

        assert not (
            "[E_job_api_to_file_crm_003]例外発生しました。" in capsys.readouterr().out
        )


@responses.activate
def test_main_get(mock_secrets, mock_api_secret, mock_parameter_store, capsys):
    """メイン"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # Secrets Managerシークレット名(DB接続情報)
        "test-db-secret",  # Managerシークレット名(DB接続情報)
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_main.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_ALL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        # "param1=data&last_timestamp=__LAST_TIMESTAMP__&current_timestamp=__CURRENT_TIMESTAMP__"
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "GET",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "sample2-1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        ["sample2-1", "sample2-2"],  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test_main",  # ファイル識別子
    ]

    secrets = boto3.client("secretsmanager")
    # 実際のDB接続情報を設定
    db_secret = {
        # "username": "dlpf_batch",
        "username": "dlpf_ope",
        "password": "password",
        "host": "************",
        "port": "5432",
        "dbname": "test_db",
    }
    secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))

    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}
    responses.add(responses.GET, mock_url, json=mock_response, status=200)
    mock_url2 = "https://api-test.com/info"
    mock_response2 = {
        "data": {
            "sample1": "data1",
            "sample2": [
                {"sample2-1-1": "data2-1-1", "sample2-1-2": "data2-1-2"},
                {"sample2-2-1": "data2-2-1", "sample2-2-2": "data2-2-2"},
            ],
            "sample3": {"sample3-1": "data3-1", "sample3-2": "data3-2"},
        }
    }
    mock_response2 = json.dumps(mock_response2, indent=4)
    responses.add(responses.GET, mock_url2, json=mock_response2, status=200)

    with patch("source.glue_job_api_to_file_crm.GlueJobApiToFileCrm.put_to_s3"):
        try:
            main()
        except SystemExit:
            assert False

        assert not (
            "[E_job_api_to_file_crm_003]例外発生しました。" in capsys.readouterr().out
        )


@responses.activate
def test_main_execute_except(
    mock_secrets, mock_api_secret, mock_parameter_store, capsys
):
    """メイン"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # Secrets Managerシークレット名(DB接続情報)
        "test-db-secret",  # Managerシークレット名(DB接続情報)
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_main.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_ALL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        "jobnet_id",  # ジョブネットID
        "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test_main",  # ファイル識別子
    ]

    secrets = boto3.client("secretsmanager")
    # 実際のDB接続情報を設定
    db_secret = {
        # "username": "dlpf_batch",
        "username": "dlpf_ope",
        "password": "password",
        "host": "************",
        "port": "5432",
        "dbname": "test_db",
    }
    secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))

    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}
    responses.add(responses.GET, mock_url, json=mock_response, status=200)
    mock_url2 = "https://api-test.com/info"
    mock_response2 = {"message": "success", "result": "data1"}
    responses.add(responses.GET, mock_url2, json=mock_response2, status=200)
    try:
        main()
    except SystemExit:
        assert "[E_job_api_to_file_crm_003]例外発生しました。" in capsys.readouterr().out


@responses.activate
def test_main_except_exit(mock_secrets, mock_api_secret, mock_parameter_store, capsys):
    """メイン"""

    sys.argv = [
        "glue_job_api_to_file_crm",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "fila_data",
        "aaa,data1,111\r\nbbb,data2,222",
        "secret_name",  # Secrets Managerシークレット名(DB接続情報)
        "test-db-secret",  # Managerシークレット名(DB接続情報)
        "api_secret_name",  # API　Secrets Managerシークレット名
        "api_test_secret",  # API　Secrets Managerシークレット名
        "diff_flag",  # 差分連携フラグ
        True,  # 差分連携フラグ
        "file_name",  # ファイル名
        "test_main.csv",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "takahashi/input-output/POS_OUT/",  # アウトプットファイルディレクトリ
        "file_setting",  # 出力ファイル属性
        '{"sep":"," , "quoting": "csv.QUOTE_ALL","quotechar": "\\"" ,"lineterminator": "\\n","header": "False","index":"False"}',  # 出力ファイル属性
        # "jobnet_id",  # ジョブネットID
        # "JN_PT001-AF01_001",  # ジョブネットID
        "api_request_param",  # 情報取得APIリクエストパラメータ
        '{"param1":"data","last_timestamp":"__LAST_TIMESTAMP__","current_timestamp":"__CURRENT_TIMESTAMP__"}',  # 情報取得APIリクエストパラメータ
        "api_request_method",  # 情報取得APIリクエストメソッド
        "POST",  # 情報取得APIリクエストメソッド
        "records_key",  # レコード特定用キー
        "rec1",  # レコード特定用キー
        "output_keys",  # 出力対象キー
        "key2",  # 出力対象キー
        "object_key",  # レコード格納オブジェクトキー
        "key3",  # レコード格納オブジェクトキー
        "file_id",  # ファイル識別子
        "test_main",  # ファイル識別子
    ]

    secrets = boto3.client("secretsmanager")
    # 実際のDB接続情報を設定
    db_secret = {
        # "username": "dlpf_batch",
        "username": "dlpf_ope",
        "password": "password",
        "host": "************",
        "port": "5432",
        "dbname": "test_db",
    }
    secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))

    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}
    responses.add(responses.GET, mock_url, json=mock_response, status=200)
    mock_url2 = "https://api-test.com/info"
    mock_response2 = {"message": "success", "result": "data1"}
    responses.add(responses.GET, mock_url2, json=mock_response2, status=200)

    with pytest.raises(SystemExit):
        main()
