SELECT p.MDM_INTEGRATION_MANAGEMENT_CD,
    p.MAIL_ORDER_PRODUCT_CD,
    p.WAREHOUSE_MANAGEMENT_CD,
    p.<PERSON><PERSON>,
    p.CORE_PRODUCT_NAME,
    p.PRODUCT_NAME,
    p.PRODUCT_SEGMENT,
    p.BUSINESS_SEGMENT,
    p.PRODUCT_CAT,
    p.PRODUCT_SERIES,
    TO_CHAR(p.SALE_START_DATE, 'YYYY/MM/DD') AS sale_start_date,
    CASE
        WHEN p.PERIOD_SET_SALES_CHANNEL_3 = '10' THEN TO_CHAR(p.SALES_CHANNEL_3_SALE_START_DATE, 'YYYY/MM/DD HH24:MI:SS')
        WHEN p.PERIOD_SET_SALES_CHANNEL_1 = '10' THEN TO_CHAR(p.SALES_CHANNEL_1_SALE_START_DATE, 'YYYY/MM/DD HH24:MI:SS')
        WHEN p.PERIOD_SET_SALES_CHANNEL_2 = '10' THEN TO_CHAR(p.SALES_CHANNEL_2_SALE_START_DATE, 'YYYY/MM/DD HH24:MI:SS')
    END AS sale_start_date_time,
    CASE
        WHEN p.PERIOD_SET_SALES_CHANNEL_1 = '10' THEN TO_CHAR(p.SALES_CHANNEL_1_SALE_END_DATE, 'YYYY/MM/DD HH24:MI:SS')
        WHEN p.PERIOD_SET_SALES_CHANNEL_3 = '10' THEN TO_CHAR(p.SALES_CHANNEL_3_SALE_END_DATE, 'YYYY/MM/DD HH24:MI:SS')
        WHEN p.PERIOD_SET_SALES_CHANNEL_2 = '10' THEN TO_CHAR(p.SALES_CHANNEL_2_SALE_END_DATE, 'YYYY/MM/DD HH24:MI:SS')
    END AS sale_end_date_time,
    pp.TAX_EXC,
    pp.TAX_INC,
    pp.TAX,
    pp.TAX_RATE,
    p.SALE_STATUS,
    p.LGROUP,
    p.MGROUP,
    p.SGROUP,
    p.DGROUP,
    p.PRODUCT_TYPE,
    p.CORE_DEPARTMENT,
    p.ACCOUNTIN_PATTERN_GB,
    p.MATERIAL,
    p.SET_PRODUCT_FLG,
    p.SET_COMPOSITION_FLG,
    p.STORE_PO_GB,
    p.REPRESENTATIVE_PRODUCT_CD,
    p.LOT_MANAGEMENT_TARGET_PRODUCT,
    p.REDUCTION_BASE,
    p.COLOR_NAME,
    CASE
        WHEN p.LGROUP IN ('30', '51') THEN SUBSTRING(p.WAREHOUSE_MANAGEMENT_CD, LENGTH(p.WAREHOUSE_MANAGEMENT_CD) - 3, 2)
        ELSE p.COLOR_CD
    END AS COLOR_CD,
    p.SIZE_NAME,
    CASE
        WHEN p.LGROUP IN ('30', '51') THEN SUBSTRING(p.WAREHOUSE_MANAGEMENT_CD, LENGTH(p.WAREHOUSE_MANAGEMENT_CD) - 1, 2)
        ELSE p.SIZE_CD
    END AS SIZE_CD,
    p.SEASON,
    p.USE_POINT_CNT,
    p.PRODUCT_NO
FROM mdm.product_linkage p
JOIN mdm.period_price_linkage AS pp ON
    p.mdm_integration_management_cd = pp.mdm_integration_management_cd AND
    pp.mdm_integration_management_cd_nk IS NOT NULL
JOIN (
    select
    mdm_integration_management_cd,
    max(apply_start_date) as max_apply_start_date
    from
    mdm.period_price_linkage as pp3
    where
    pp3.mdm_integration_management_cd_nk IS NOT NULL and
    pp3.apply_start_date <= :diff_base_timestamp
    group by mdm_integration_management_cd
) as pp2 ON
pp.mdm_integration_management_cd = pp2.mdm_integration_management_cd AND
pp.apply_start_date = pp2.max_apply_start_date
WHERE
(
  (
      p.PMS_U_YMD > :sync_datetime
      AND p.PMS_U_YMD <= :diff_base_timestamp
  )
    OR TO_CHAR(pp.apply_start_date, 'YYYYMMDD') = TO_CHAR(:diff_base_timestamp, 'YYYYMMDD')
)
AND
(
  p.PERIOD_SET_SALES_CHANNEL_1 = "10"
  OR p.PERIOD_SET_SALES_CHANNEL_2 = "10"
  OR p.PERIOD_SET_SALES_CHANNEL_3 = "10"
)
AND p.MAIL_ORDER_PRODUCT_CD IS NOT NULL
;
