SELECT order_no,
    order_detail_no,
    shop_code,
    sku_code,
    commodity_code,
    commodity_name,
    commodity_kind,
    baitai_code,
    baitai_name,
    hinban_code,
    standard_detail1_name,
    standard_detail2_name,
    purchasing_amount,
    unit_price,
    retail_price,
    retail_tax,
    commodity_tax_group_code,
    commodity_tax_no,
    commodity_tax_rate,
    commodity_tax,
    commodity_tax_type,
    campaign_code,
    campaign_name,
    campaign_instructions_code,
    campaign_instructions_name,
    campaign_discount_rate,
    campaign_discount_price,
    present_campaign_instructions_code,
    present_order_detail_no,
    age_limit_code,
    age_limit_name,
    age,
    age_limit_confirm_type,
    applied_point_rate,
    benefits_code,
    benefits_name,
    benefits_commodity_code,
    stock_management_type,
    stock_allocated_kbn,
    allocated_warehouse_code,
    allocated_quantity,
    arrival_reserved_quantity,
    cancel_quantity,
    henpin_qt,
    coupon_management_code,
    coupon_code,
    coupon_name,
    coupon_discount_rate,
    coupon_discount_price,
    ec_promotion_id,
    ec_promotion_name,
    ec_promotion_discount_price,
    ec_campaign_id,
    ec_campaign_name,
    adjustment_price,
    keihi_hurikae_target_flg,
    member_price_applied_flg,
    shipping_charge_target_flg,
    regular_contract_no,
    regular_contract_detail_no,
    regular_kaiji,
    regular_check_memo,
    total_commodity_buy_count,
    total_commodity_regular_kaiji,
    regular_total_commodity_regular_kaiji,
    commodity_category_code,
    total_category_buy_count,
    total_categoryregular_kaiji,
    regular_total_categoryregular_kaiji,
    commodity_subcategory_code,
    total_subcategory_buy_count,
    total_subcategoryregular_kaiji,
    regular_total_subcategoryregular_kaiji,
    commodity_subsubcategory_code,
    total_subsubcategory_buy_count,
    total_subsubcategoryregular_kaiji,
    regular_total_subsubcategoryregular_kaiji,
    grant_plan_point_prod_detail,
    reduction_plan_point_prod_detail,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg
FROM order_detail_view
where updated_datetime > :sync_datetime
    AND updated_datetime <= :diff_base_timestamp;
