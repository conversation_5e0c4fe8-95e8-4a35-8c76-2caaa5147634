CREATE TABLE wk_pr003_df01_set_commodity_volume_list_work (
    mail_order_product_cd VARCHAR(16) NOT NULL,
    weight NUMERIC(7,2),
    nekoposu_volume_rate INTEGER,
    outside_home_volume_rate INTEGER,
    PRIMARY KEY (mail_order_product_cd)
);
COMMENT ON TABLE wk_pr003_df01_set_commodity_volume_list_work IS '商品連携_セット商品体積情報ワークテーブル';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_volume_list_work.mail_order_product_cd IS '通販商品番号';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_volume_list_work.weight IS '重さ';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_volume_list_work.nekoposu_volume_rate IS 'ネコポス体積率';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_volume_list_work.outside_home_volume_rate IS '自宅外受取体積率';

